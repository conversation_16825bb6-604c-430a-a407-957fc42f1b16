'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { familiesApi, offeringsApi, offeringTypesApi } from '@/lib/api';
import { Loader2, Printer } from 'lucide-react';
import { useReactToPrint } from 'react-to-print';

interface Family {
  _id: string;
  headName: string;
  slNo: string;
}

interface OfferingReceipt {
  _id: string;
  date: string;
  familyId: {
    _id: string;
    headName: string;
    slNo: string;
  };
  type: string;
  amount: number;
  purpose: string;
  notes?: string;
  createdAt: string;
}

export default function AddOfferingPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
    familyId: '',
    type: 'Sunday Collection',
    amount: '',
    purpose: '',
    notes: ''
  });
  const [families, setFamilies] = useState<Family[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submittedOffering, setSubmittedOffering] = useState<OfferingReceipt | null>(null);
  const [showReceipt, setShowReceipt] = useState(false);
  const [generateBill, setGenerateBill] = useState(false);
  const [offeringTypes, setOfferingTypes] = useState<{_id: string, name: string}[]>([]);
  
  const receiptRef = useRef<HTMLDivElement>(null); // Create a ref for the receipt element

  // Fetch families and offering types from API
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const [familiesData, offeringTypesData] = await Promise.all([
          familiesApi.getAll(),
          offeringTypesApi.getAll()
        ]);
        
        setFamilies(Array.isArray(familiesData) ? familiesData : []);
        
        // Filter to only active offering types
        const activeTypes = Array.isArray(offeringTypesData) 
          ? offeringTypesData.filter(type => type.isActive)
          : [];
          
        setOfferingTypes(activeTypes);
        
        // Set default type if available
        if (activeTypes.length > 0) {
          setFormData(prev => ({
            ...prev,
            type: activeTypes[0].name
          }));
        }
      } catch (err) {
        console.error('Failed to fetch data:', err);
        setError('Failed to load data. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // Send data to backend
      const response = await offeringsApi.create(formData);
      
      // Get the complete offering data with family details for the receipt
      const offeringWithDetails = await offeringsApi.getById(response._id);
      setSubmittedOffering(offeringWithDetails);
      setShowReceipt(true);
    } catch (err) {
      console.error('Error submitting offering:', err);
      setError('Failed to submit offering. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePrint = useReactToPrint({
    contentRef: receiptRef,
    documentTitle: `Offering_Receipt_${submittedOffering?._id || 'new'}`,
    onAfterPrint: () => {
      // Redirect back to offerings list after printing
      router.push('/admin/offerings');
    }
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const toggleBillGeneration = () => {
    setGenerateBill(!generateBill);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2">Loading families...</span>
      </div>
    );
  }

  if (showReceipt && submittedOffering) {
    return (
      <div className="p-4">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Offering Receipt</h1>
          <div className="flex gap-4">
            <button
              onClick={handlePrint}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center"
            >
              <Printer className="h-4 w-4 mr-2" />
              Print {generateBill ? "Bill" : "Receipt"}
            </button>
            <button
              onClick={() => router.push('/admin/offerings')}
              className="bg-gray-300 text-gray-800 px-4 py-2 rounded hover:bg-gray-400"
            >
              Back to Offerings
            </button>
          </div>
        </div>
        
        {/* Receipt/Bill Template */}
        <div ref={receiptRef} className="bg-white p-8 rounded-lg shadow-md max-w-2xl mx-auto">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold">My Parish</h2>
            <p className="text-gray-600">
              {generateBill ? "Official Bill" : "Official Offering Receipt"}
            </p>
          </div>
          
          <div className="border-t border-b border-gray-200 py-4 mb-6">
            <div className="flex justify-between mb-2">
              <span className="font-semibold">{generateBill ? "Bill No:" : "Receipt No:"}</span>
              <span>{submittedOffering._id.substring(0, 8).toUpperCase()}</span>
            </div>
            <div className="flex justify-between mb-2">
              <span className="font-semibold">Date:</span>
              <span>{formatDate(submittedOffering.date)}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-semibold">Family:</span>
              <span>{submittedOffering.familyId.slNo} - {submittedOffering.familyId.headName}</span>
            </div>
          </div>
          
          <div className="mb-6">
            <h3 className="font-semibold mb-2">{generateBill ? "Bill Details" : "Offering Details"}</h3>
            <div className="bg-gray-50 p-4 rounded">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-gray-600">Type</p>
                  <p className="font-medium">{submittedOffering.type}</p>
                </div>
                <div>
                  <p className="text-gray-600">Amount</p>
                  <p className="font-medium">${Number(submittedOffering.amount).toFixed(2)}</p>
                </div>
                {submittedOffering.purpose && (
                  <div className="col-span-2">
                    <p className="text-gray-600">Purpose</p>
                    <p className="font-medium">{submittedOffering.purpose}</p>
                  </div>
                )}
                {submittedOffering.notes && (
                  <div className="col-span-2">
                    <p className="text-gray-600">Notes</p>
                    <p className="font-medium">{submittedOffering.notes}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          <div className="text-center text-gray-500 text-sm mt-8">
            {generateBill ? (
              <p>Please pay the above amount at your earliest convenience.</p>
            ) : (
              <p>Thank you for your generous contribution!</p>
            )}
            <p className="mt-1">This {generateBill ? "bill" : "receipt"} was generated on {formatDate(new Date().toISOString())}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">Record New Offering</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      <div className="bg-white p-6 rounded-lg shadow-md">
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label className="block text-sm font-medium mb-1">Date</label>
              <input
                type="date"
                name="date"
                value={formData.date}
                onChange={handleChange}
                className="w-full p-2 border rounded"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Family</label>
              <select
                name="familyId"
                value={formData.familyId}
                onChange={handleChange}
                className="w-full p-2 border rounded"
                required
                disabled={families.length === 0}
              >
                <option value="">Select a family</option>
                {families.map(family => (
                  <option key={family._id} value={family._id}>
                    {family.slNo} - {family.headName}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Offering Type</label>
              <select
                name="type"
                value={formData.type}
                onChange={handleChange}
                className="w-full p-2 border rounded"
                required
              >
                {offeringTypes.length > 0 ? (
                  offeringTypes.map(type => (
                    <option key={type._id} value={type.name}>
                      {type.name}
                    </option>
                  ))
                ) : (
                  <option value="Sunday Collection">Sunday Collection</option>
                )}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Amount</label>
              <input
                type="number"
                name="amount"
                value={formData.amount}
                onChange={handleChange}
                className="w-full p-2 border rounded"
                min="0"
                step="0.01"
                required
              />
            </div>
            
            <div className="md:col-span-2">
              <label className="block text-sm font-medium mb-1">Purpose</label>
              <input
                type="text"
                name="purpose"
                value={formData.purpose}
                onChange={handleChange}
                className="w-full p-2 border rounded"
              />
            </div>
            
            <div className="md:col-span-2">
              <label className="block text-sm font-medium mb-1">Notes</label>
              <textarea
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                className="w-full p-2 border rounded"
                rows={3}
              ></textarea>
            </div>
          </div>
          
          <div className="md:col-span-2">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="generateBill"
                checked={generateBill}
                onChange={toggleBillGeneration}
                className="h-4 w-4 text-blue-600 border-gray-300 rounded"
              />
              <label htmlFor="generateBill" className="ml-2 block text-sm text-gray-700">
                Generate bill instead of receipt
              </label>
            </div>
          </div>
          
          <div className="flex justify-end">
            <button
              type="button"
              onClick={() => router.back()}
              className="bg-gray-300 text-gray-800 px-4 py-2 rounded mr-2 hover:bg-gray-400"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Submitting...
                </>
              ) : (
                'Record Offering'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
