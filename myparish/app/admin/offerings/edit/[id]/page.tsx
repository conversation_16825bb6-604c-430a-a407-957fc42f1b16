'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { familiesApi, offeringsApi, offeringTypesApi } from '@/lib/api';
import { Loader2, ArrowLeft } from 'lucide-react';

interface Family {
  _id: string;
  headName: string;
  slNo: string;
}

interface Offering {
  _id: string;
  date: string;
  familyId: string | { _id: string; headName: string; slNo: string };
  type: string;
  amount: number;
  purpose: string;
  notes?: string;
}

export default function EditOfferingPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [formData, setFormData] = useState({
    date: '',
    familyId: '',
    type: '',
    amount: '',
    purpose: '',
    notes: ''
  });
  const [families, setFamilies] = useState<Family[]>([]);
  const [offeringTypes, setOfferingTypes] = useState<{_id: string, name: string}[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        
        // Fetch families, offering types, and offering details in parallel
        const [familiesData, offeringTypesData, offeringData] = await Promise.all([
          familiesApi.getAll(),
          offeringTypesApi.getAll(),
          offeringsApi.getById(params.id)
        ]);
        
        setFamilies(Array.isArray(familiesData) ? familiesData : []);
        
        // Filter to only active offering types
        const activeTypes = Array.isArray(offeringTypesData) 
          ? offeringTypesData.filter(type => type.isActive)
          : [];
        
        setOfferingTypes(activeTypes);
        
        // Format the data for the form
        setFormData({
          date: new Date(offeringData.date).toISOString().split('T')[0],
          familyId: typeof offeringData.familyId === 'string' 
            ? offeringData.familyId 
            : offeringData.familyId._id,
          type: offeringData.type,
          amount: offeringData.amount.toString(),
          purpose: offeringData.purpose || '',
          notes: offeringData.notes || ''
        });
      } catch (err) {
        console.error('Failed to fetch data:', err);
        setError('Failed to load data. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [params.id]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // Send updated data to backend
      await offeringsApi.update(params.id, formData);
      
      // Redirect to offering details page
      router.push(`/admin/offerings/${params.id}`);
    } catch (err) {
      console.error('Error updating offering:', err);
      setError('Failed to update offering. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2">Loading offering data...</span>
      </div>
    );
  }

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">Edit Offering</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      <div className="bg-white p-6 rounded-lg shadow-md">
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="date">
                Date
              </label>
              <input
                type="date"
                id="date"
                name="date"
                value={formData.date}
                onChange={handleChange}
                className="w-full p-2 border rounded"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="familyId">
                Family
              </label>
              <select
                id="familyId"
                name="familyId"
                value={formData.familyId}
                onChange={handleChange}
                className="w-full p-2 border rounded"
                required
              >
                <option value="">Select a family</option>
                {families.map(family => (
                  <option key={family._id} value={family._id}>
                    {family.slNo} - {family.headName}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="type">
                Offering Type
              </label>
              <select
                id="type"
                name="type"
                value={formData.type}
                onChange={handleChange}
                className="w-full p-2 border rounded"
                required
              >
                {offeringTypes.length > 0 ? (
                  offeringTypes.map(type => (
                    <option key={type._id} value={type.name}>
                      {type.name}
                    </option>
                  ))
                ) : (
                  <option value="Sunday Collection">Sunday Collection</option>
                )}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="amount">
                Amount
              </label>
              <input
                type="number"
                id="amount"
                name="amount"
                value={formData.amount}
                onChange={handleChange}
                className="w-full p-2 border rounded"
                min="0"
                step="0.01"
                required
              />
            </div>
            
            <div className="md:col-span-2">
              <label className="block text-sm font-medium mb-1" htmlFor="purpose">
                Purpose
              </label>
              <input
                type="text"
                id="purpose"
                name="purpose"
                value={formData.purpose}
                onChange={handleChange}
                className="w-full p-2 border rounded"
                placeholder="Purpose of the offering"
              />
            </div>
            
            <div className="md:col-span-2">
              <label className="block text-sm font-medium mb-1" htmlFor="notes">
                Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                className="w-full p-2 border rounded"
                rows={3}
                placeholder="Additional notes (optional)"
              />
            </div>
          </div>
          
          <div className="flex justify-end">
            <Link
              href={`/admin/offerings/${params.id}`}
              className="bg-gray-300 text-gray-800 px-4 py-2 rounded mr-2 hover:bg-gray-400 inline-flex items-center"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Cancel
            </Link>
            <button
              type="submit"
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Updating...
                </>
              ) : (
                'Update Offering'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
