'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { offeringsApi, familiesApi, offeringTypesApi } from '@/lib/api';
import { Edit, Eye, Trash2, Loader2, Printer, ChevronDown, FileText, Filter, Search, Plus } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useReactToPrint } from 'react-to-print';
import Swal from 'sweetalert2';
import { ToastContainer, toast } from 'react-toastify';
import 'sweetalert2/dist/sweetalert2.min.css';
import 'react-toastify/dist/ReactToastify.css';

interface Family {
  _id: string;
  headName: string;
  slNo: string;
}

interface Offering {
  _id: string;
  date: string;
  familyId: {
    _id: string;
    headName: string;
    slNo: string;
  };
  type: string;
  amount: number;
  purpose: string;
  notes?: string;
}

// Add a new component for the printable receipt
const PrintableReceipt = ({ offering, generateBill = false, receiptRef }: { 
  offering: Offering; 
  generateBill?: boolean; 
  receiptRef: React.RefObject<HTMLDivElement>; 
}) => {
  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div ref={receiptRef} style={{display: 'none'}}>
      <div className="bg-white p-8 rounded-lg shadow-md max-w-2xl mx-auto">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold">My Parish</h2>
          <p className="text-gray-600">
            {generateBill ? "Official Bill" : "Official Offering Receipt"}
          </p>
        </div>
        
        <div className="border-t border-b border-gray-200 py-4 mb-6">
          <div className="flex justify-between mb-2">
            <span className="font-semibold">{generateBill ? "Bill No:" : "Receipt No:"}</span>
            <span>{offering._id.substring(0, 8).toUpperCase()}</span>
          </div>
          <div className="flex justify-between mb-2">
            <span className="font-semibold">Date:</span>
            <span>{formatDate(offering.date)}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-semibold">Family:</span>
            <span>{offering.familyId.slNo} - {offering.familyId.headName}</span>
          </div>
        </div>
        
        <div className="mb-6">
          <h3 className="font-semibold mb-2">{generateBill ? "Bill Details" : "Offering Details"}</h3>
          <div className="bg-gray-50 p-4 rounded">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-gray-600">Type</p>
                <p className="font-medium">{offering.type}</p>
              </div>
              <div>
                <p className="text-gray-600">Amount</p>
                <p className="font-medium">${Number(offering.amount).toFixed(2)}</p>
              </div>
              {offering.purpose && (
                <div className="col-span-2">
                  <p className="text-gray-600">Purpose</p>
                  <p className="font-medium">{offering.purpose}</p>
                </div>
              )}
              {offering.notes && (
                <div className="col-span-2">
                  <p className="text-gray-600">Notes</p>
                  <p className="font-medium">{offering.notes}</p>
                </div>
              )}
            </div>
          </div>
        </div>
        
        <div className="text-center text-gray-500 text-sm mt-8">
          {generateBill ? (
            <p>Please pay the above amount at your earliest convenience.</p>
          ) : (
            <p>Thank you for your generous contribution!</p>
          )}
          <p className="mt-1">This {generateBill ? "bill" : "receipt"} was generated on {formatDate(new Date().toISOString())}</p>
        </div>
      </div>
    </div>
  );
};

export default function AdminOfferingsPage() {
  const [offerings, setOfferings] = useState<Offering[]>([]);
  const [families, setFamilies] = useState<Family[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [dateRange, setDateRange] = useState('all');
  const [offeringType, setOfferingType] = useState('all');
  const [selectedFamily, setSelectedFamily] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const dataFetched = useRef(false);
  const [selectedOffering, setSelectedOffering] = useState<Offering | null>(null);
  const [printAsBill, setPrintAsBill] = useState(false);
  const receiptRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const [offeringTypes, setOfferingTypes] = useState<{_id: string, name: string}[]>([]);

  const handlePrint = useReactToPrint({
    contentRef: receiptRef,
    documentTitle: `Offering_${selectedOffering?._id || 'receipt'}`,
    onAfterPrint: () => {
      setSelectedOffering(null);
    }
  });

  const prepareForPrint = (offering: Offering, asBill = false) => {
    setSelectedOffering(offering);
    setPrintAsBill(asBill);
    
    // Give time for the component to render before printing
    setTimeout(() => {
      handlePrint();
    }, 100);
  };

  const handleDelete = async (id: string) => {
    const result = await Swal.fire({
      title: 'Are you sure?',
      text: 'You won’t be able to revert this!',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!',
    });

    if (result.isConfirmed) {
      try {
        setIsLoading(true);
        await offeringsApi.delete(id); // Assuming `offeringsApi.delete` is the API call to delete an offering
        setOfferings(offerings.filter((offering) => offering._id !== id));
        toast.success('Offering deleted successfully', {
          position: 'top-right',
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });
      } catch (error) {
        console.error('Failed to delete offering:', error);
        toast.error('Failed to delete offering', {
          position: 'top-right',
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });
      } finally {
        setIsLoading(false);
      }
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      if (dataFetched.current) return;
      
      try {
        setIsLoading(true);
        const [offeringsData, familiesData, offeringTypesData] = await Promise.all([
          offeringsApi.getAll(),
          familiesApi.getAll(),
          offeringTypesApi.getAll()
        ]);
        
        setOfferings(offeringsData);
        setFamilies(familiesData);
        
        // Filter to only active offering types
        const activeTypes = Array.isArray(offeringTypesData) 
          ? offeringTypesData.filter(type => type.isActive)
          : [];
          
        setOfferingTypes(activeTypes);
        
        dataFetched.current = true;
      } catch (error) {
        console.error('Failed to fetch data:', error);
        setError('Failed to load data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Filter offerings based on search and filters
  const filteredOfferings = offerings.filter(offering => {
    // Search term filter
    const searchMatch = 
      offering.familyId.headName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      offering.purpose.toLowerCase().includes(searchTerm.toLowerCase()) ||
      offering.type.toLowerCase().includes(searchTerm.toLowerCase());
    
    // Type filter
    const typeMatch = offeringType === 'all' || offering.type === offeringType;
    
    // Family filter
    const familyMatch = selectedFamily === 'all' || offering.familyId._id === selectedFamily;
    
    // Date filter (simplified for now)
    let dateMatch = true;
    if (dateRange !== 'all') {
      const offeringDate = new Date(offering.date);
      const now = new Date();
      
      if (dateRange === 'thisMonth') {
        dateMatch = offeringDate.getMonth() === now.getMonth() && 
                    offeringDate.getFullYear() === now.getFullYear();
      } else if (dateRange === 'lastMonth') {
        const lastMonth = now.getMonth() === 0 ? 11 : now.getMonth() - 1;
        const lastMonthYear = now.getMonth() === 0 ? now.getFullYear() - 1 : now.getFullYear();
        dateMatch = offeringDate.getMonth() === lastMonth && 
                    offeringDate.getFullYear() === lastMonthYear;
      } else if (dateRange === 'thisYear') {
        dateMatch = offeringDate.getFullYear() === now.getFullYear();
      }
    }
    
    return searchMatch && typeMatch && dateMatch && familyMatch;
  });

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h1 className="text-3xl font-bold mb-6 text-gray-800">Manage Offerings</h1>

      {/* Add the printable component - always render it but keep it hidden */}
      <div style={{position: 'absolute', width: '100%', height: '0', overflow: 'hidden'}}>
        {selectedOffering && (
          <PrintableReceipt 
            offering={selectedOffering} 
            generateBill={printAsBill} 
            receiptRef={receiptRef as React.RefObject<HTMLDivElement>}
          />
        )}
      </div>

      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-700">Offerings Registry</h2>
            <p className="text-gray-500">Manage all parish offerings and contributions</p>
          </div>
          <Link
            href="/admin/offerings/add"
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center"
          >
            <Plus className="mr-2" size={18} />
            Record New Offering
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium mb-1 text-gray-600">Date Range</label>
            <select
              className="w-full p-2 border rounded-lg focus:ring focus:ring-blue-300"
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
            >
              <option value="all">All Time</option>
              <option value="thisMonth">This Month</option>
              <option value="lastMonth">Last Month</option>
              <option value="thisYear">This Year</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1 text-gray-600">Offering Type</label>
            <select
              className="w-full p-2 border rounded-lg focus:ring focus:ring-blue-300"
              value={offeringType}
              onChange={(e) => setOfferingType(e.target.value)}
            >
              <option value="all">All Types</option>
              {offeringTypes.length > 0 ? (
                offeringTypes.map(type => (
                  <option key={type._id} value={type.name}>
                    {type.name}
                  </option>
                ))
              ) : (
                <>
                  <option value="Sunday Collection">Sunday Collection</option>
                  <option value="Special Donation">Special Donation</option>
                  <option value="Tithe">Tithe</option>
                  <option value="Building Fund">Building Fund</option>
                  <option value="Mission Fund">Mission Fund</option>
                  <option value="Youth Ministry">Youth Ministry</option>
                  <option value="Other">Other</option>
                </>
              )}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1 text-gray-600">Family</label>
            <select
              className="w-full p-2 border rounded-lg focus:ring focus:ring-blue-300"
              value={selectedFamily}
              onChange={(e) => setSelectedFamily(e.target.value)}
            >
              <option value="all">All Families</option>
              {families && families.length > 0 ? (
                families.map(family => (
                  <option key={family._id} value={family._id}>
                    {family.slNo} - {family.headName}
                  </option>
                ))
              ) : (
                <option disabled>Loading families...</option>
              )}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1 text-gray-600">Search</label>
            <div className="relative">
              <input
                type="text"
                placeholder="Search..."
                className="w-full p-2 border rounded-lg focus:ring focus:ring-blue-300"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <Search className="absolute top-2.5 right-3 text-gray-400" size={18} />
            </div>
          </div>
        </div>

        {isLoading ? (
          <div className="flex justify-center py-8">
            <Loader2 size={32} className="animate-spin text-blue-500" />
          </div>
        ) : error ? (
          <div className="text-center py-8 text-red-500">{error}</div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white border rounded-lg">
              <thead>
                <tr className="bg-gray-100">
                  <th className="py-3 px-4 text-left text-gray-600 font-medium">ID</th>
                  <th className="py-3 px-4 text-left text-gray-600 font-medium">Date</th>
                  <th className="py-3 px-4 text-left text-gray-600 font-medium">Family</th>
                  <th className="py-3 px-4 text-left text-gray-600 font-medium">Type</th>
                  <th className="py-3 px-4 text-left text-gray-600 font-medium">Amount</th>
                  <th className="py-3 px-4 text-left text-gray-600 font-medium">Purpose</th>
                  <th className="py-3 px-4 text-left text-gray-600 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredOfferings.length === 0 ? (
                  <tr>
                    <td className="py-4 px-4 text-center text-gray-500" colSpan={7}>
                      No offerings found matching your criteria.
                    </td>
                  </tr>
                ) : (
                  filteredOfferings.map((offering) => (
                    <tr key={offering._id} className="border-t hover:bg-gray-50 transition">
                      <td className="py-3 px-4">{offering._id.substring(0, 8)}...</td>
                      <td className="py-3 px-4">{formatDate(offering.date)}</td>
                      <td className="py-3 px-4">{offering.familyId.slNo} - {offering.familyId.headName}</td>
                      <td className="py-3 px-4">
                        <span className="bg-blue-100 text-blue-600 px-2 py-1 rounded-full text-xs">
                          {offering.type}
                        </span>
                      </td>
                      <td className="py-3 px-4 font-medium">{formatCurrency(offering.amount)}</td>
                      <td className="py-3 px-4">{offering.purpose}</td>
                      <td className="py-3 px-4">
                        <div className="flex space-x-2">
                          <Link href={`/admin/offerings/${offering._id}`} className="text-blue-600 hover:text-blue-800">
                            <Eye size={18} />
                          </Link>
                          <Link href={`/admin/offerings/edit/${offering._id}`} className="text-yellow-600 hover:text-yellow-800">
                            <Edit size={18} />
                          </Link>
                          <button 
                            className="text-red-600 hover:text-red-800"
                            onClick={() => handleDelete(offering._id)}
                          >
                            <Trash2 size={18} />
                          </button>
                          {/* Add print buttons */}
                          <div className="relative group">
                            <button 
                              className="text-green-600 hover:text-green-800 flex items-center"
                              onClick={() => prepareForPrint(offering, false)}
                              title="Print Options"
                            >
                              <Printer size={18} />
                              <ChevronDown size={14} className="ml-1" />
                            </button>
                            <div className="absolute hidden group-hover:block bg-white shadow-md p-2 rounded z-10 right-0 mt-1 w-36 border border-gray-200">
                              <button 
                                className="flex w-full text-left px-2 py-1 hover:bg-gray-100 text-sm items-center"
                                onClick={() => prepareForPrint(offering, false)}
                              >
                                <FileText size={14} className="mr-2" />
                                Receipt
                              </button>
                              <button 
                                className="flex w-full text-left px-2 py-1 hover:bg-gray-100 text-sm items-center"
                                onClick={() => prepareForPrint(offering, true)}
                              >
                                <FileText size={14} className="mr-2" />
                                Bill
                              </button>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Toast Container */}
      <ToastContainer />
    </div>
  );
}
