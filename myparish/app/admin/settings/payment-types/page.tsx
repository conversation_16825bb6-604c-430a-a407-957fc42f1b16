'use client';

import { useState, useEffect } from 'react';
import { paymentTypesApi } from '@/lib/api';
import { useTheme } from '../../../context/ThemeContext';
import { Loader2, Plus, Edit, Trash2, Save, X, DollarSign, Calendar, CheckCircle, XCircle } from 'lucide-react';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

interface PaymentType {
  _id: string;
  name: string;
  description: string;
  amount: number;
  frequency: string;
  isActive: boolean;
}

export default function PaymentTypesPage() {
  const { colors } = useTheme();
  const [paymentTypes, setPaymentTypes] = useState<PaymentType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [activeType, setActiveType] = useState<PaymentType | null>(null);
  const [editForm, setEditForm] = useState({
    name: '',
    description: '',
    amount: 0,
    frequency: 'yearly',
    isActive: true
  });
  const [isAdding, setIsAdding] = useState(false);

  useEffect(() => {
    fetchPaymentTypes();
  }, []);

  const fetchPaymentTypes = async () => {
    try {
      setIsLoading(true);
      const data = await paymentTypesApi.getAll();
      setPaymentTypes(data);
    } catch (error) {
      console.error('Failed to fetch payment types:', error);
      toast.error('Failed to load payment types');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddType = async () => {
    if (!editForm.name.trim()) {
      toast.error('Type name cannot be empty');
      return;
    }

    if (editForm.amount <= 0) {
      toast.error('Amount must be greater than zero');
      return;
    }

    try {
      setIsLoading(true);
      const newType = await paymentTypesApi.create({
        name: editForm.name,
        description: editForm.description,
        amount: editForm.amount,
        frequency: editForm.frequency
      });
      setPaymentTypes([...paymentTypes, newType]);
      setEditForm({
        name: '',
        description: '',
        amount: 0,
        frequency: 'yearly',
        isActive: true
      });
      setIsAdding(false);
      setIsDrawerOpen(false);
      toast.success('Payment type added successfully');
    } catch (error) {
      console.error('Failed to add payment type:', error);
      toast.error('Failed to add payment type');
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateType = async () => {
    if (!activeType) return;
    if (!editForm.name.trim()) {
      toast.error('Type name cannot be empty');
      return;
    }

    if (editForm.amount <= 0) {
      toast.error('Amount must be greater than zero');
      return;
    }

    try {
      setIsLoading(true);
      const updatedType = await paymentTypesApi.update(activeType._id, {
        name: editForm.name,
        description: editForm.description,
        amount: editForm.amount,
        frequency: editForm.frequency,
        isActive: editForm.isActive
      });
      
      setPaymentTypes(paymentTypes.map(type => 
        type._id === activeType._id ? updatedType : type
      ));
      
      setIsDrawerOpen(false);
      toast.success('Payment type updated successfully');
    } catch (error) {
      console.error('Failed to update payment type:', error);
      toast.error('Failed to update payment type');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteType = async (id: string) => {
    if (!confirm('Are you sure you want to delete this payment type?')) {
      return;
    }

    try {
      setIsLoading(true);
      await paymentTypesApi.delete(id);
      setPaymentTypes(paymentTypes.filter(type => type._id !== id));
      setIsDrawerOpen(false);
      toast.success('Payment type deleted successfully');
    } catch (error) {
      console.error('Failed to delete payment type:', error);
      toast.error('Failed to delete payment type');
    } finally {
      setIsLoading(false);
    }
  };

  const openDrawer = (type: PaymentType | null) => {
    if (type) {
      setActiveType(type);
      setEditForm({
        name: type.name,
        description: type.description || '',
        amount: type.amount,
        frequency: type.frequency,
        isActive: type.isActive
      });
      setIsAdding(false);
    } else {
      setActiveType(null);
      setEditForm({
        name: '',
        description: '',
        amount: 0,
        frequency: 'yearly',
        isActive: true
      });
      setIsAdding(true);
    }
    setIsDrawerOpen(true);
  };

  const closeDrawer = () => {
    setIsDrawerOpen(false);
  };

  const getFrequencyLabel = (frequency: string) => {
    switch (frequency) {
      case 'yearly': return 'Yearly';
      case 'monthly': return 'Monthly';
      case 'quarterly': return 'Quarterly';
      case 'one-time': return 'One-time';
      default: return frequency;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  if (isLoading && !paymentTypes.length) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2">Loading payment types...</span>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-xl font-semibold">Payment Types</h1>
        <button
          onClick={() => openDrawer(null)}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center"
        >
          <Plus size={18} className="mr-2" />
          Add New Type
        </button>
      </div>

      {/* Payment Types Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {paymentTypes.map(type => (
          <div 
            key={type._id}
            className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-all cursor-pointer card"
            onClick={() => openDrawer(type)}
            style={{
              borderTopWidth: '4px',
              borderTopStyle: 'solid',
              borderTopColor: type.isActive ? colors.primary : colors.error,
              borderLeftColor: activeType?._id === type._id ? colors.primary : 'transparent',
              borderRightColor: activeType?._id === type._id ? colors.primary : 'transparent',
              borderBottomColor: activeType?._id === type._id ? colors.primary : 'transparent',
              boxShadow: activeType?._id === type._id ? `0 0 0 2px ${colors.primary}30` : undefined
            }}
          >
            <div className="flex justify-between items-start">
              <div className="flex items-center">
                <div 
                  className="p-2 rounded-lg mr-3"
                  style={{ 
                    backgroundColor: `${type.isActive ? colors.primary : colors.error}15`, 
                    color: type.isActive ? colors.primary : colors.error 
                  }}
                >
                  <DollarSign size={20} />
                </div>
                <div>
                  <h3 className="font-medium text-lg">{type.name}</h3>
                  <div className="flex flex-col">
                    <span className="text-sm text-gray-600">{formatCurrency(type.amount)} • {getFrequencyLabel(type.frequency)}</span>
                    <span 
                      className={`text-sm ${type.isActive ? 'text-green-600' : 'text-red-600'}`}
                    >
                      {type.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex space-x-2">
                <button 
                  onClick={(e) => {
                    e.stopPropagation();
                    openDrawer(type);
                  }}
                  className="text-blue-600 hover:text-blue-800"
                >
                  <Edit size={18} />
                </button>
              </div>
            </div>
            {type.description && (
              <p className="mt-3 text-sm text-gray-600 line-clamp-2">{type.description}</p>
            )}
          </div>
        ))}

        {paymentTypes.length === 0 && (
          <div className="col-span-full bg-white p-6 rounded-lg shadow-md text-center">
            <DollarSign size={40} className="mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-700">No Payment Types</h3>
            <p className="text-gray-500 mb-4">You haven't added any payment types yet.</p>
            <button
              onClick={() => openDrawer(null)}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 inline-flex items-center"
            >
              <Plus size={18} className="mr-2" />
              Add Your First Type
            </button>
          </div>
        )}
      </div>

      {/* Settings Drawer */}
      <div 
        className={`fixed inset-y-0 right-0 z-50 w-full md:w-1/2 lg:w-1/3 bg-white shadow-xl transform transition-transform duration-300 ease-in-out ${
          isDrawerOpen ? 'translate-x-0' : 'translate-x-full'
        } overflow-y-auto`}
        style={{ borderLeft: `1px solid ${colors.border}` }}
      >
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <div 
                className="p-2 rounded-lg mr-3"
                style={{ 
                  backgroundColor: `${colors.primary}15`, 
                  color: colors.primary 
                }}
              >
                <DollarSign size={24} />
              </div>
              <h2 className="text-xl font-semibold">
                {isAdding ? 'Add Payment Type' : 'Edit Payment Type'}
              </h2>
            </div>
            <button
              onClick={closeDrawer}
              className="text-gray-500 hover:text-gray-700"
            >
              <X size={24} />
            </button>
          </div>

          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="typeName">
                Type Name
              </label>
              <input
                type="text"
                id="typeName"
                value={editForm.name}
                onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter payment type name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="description">
                Description
              </label>
              <textarea
                id="description"
                value={editForm.description}
                onChange={(e) => setEditForm({ ...editForm, description: e.target.value })}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter description"
                rows={3}
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="amount">
                Amount
              </label>
              <input
                type="number"
                id="amount"
                value={editForm.amount}
                onChange={(e) => setEditForm({ ...editForm, amount: parseFloat(e.target.value) })}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter amount"
                min="0"
                step="0.01"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="frequency">
                Frequency
              </label>
              <select
                id="frequency"
                value={editForm.frequency}
                onChange={(e) => setEditForm({ ...editForm, frequency: e.target.value })}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="yearly">Yearly</option>
                <option value="monthly">Monthly</option>
                <option value="quarterly">Quarterly</option>
                <option value="one-time">One-time</option>
              </select>
            </div>

            {!isAdding && (
              <div>
                <label className="block text-sm font-medium mb-1">Status</label>
                <div className="flex space-x-4 mt-2">
                  <label className="inline-flex items-center">
                    <input
                      type="radio"
                      className="form-radio text-blue-600"
                      checked={editForm.isActive}
                      onChange={() => setEditForm({ ...editForm, isActive: true })}
                    />
                    <span className="ml-2 flex items-center">
                      <CheckCircle size={16} className="text-green-600 mr-1" />
                      Active
                    </span>
                  </label>
                  <label className="inline-flex items-center">
                    <input
                      type="radio"
                      className="form-radio text-red-600"
                      checked={!editForm.isActive}
                      onChange={() => setEditForm({ ...editForm, isActive: false })}
                    />
                    <span className="ml-2 flex items-center">
                      <XCircle size={16} className="text-red-600 mr-1" />
                      Inactive
                    </span>
                  </label>
                </div>
              </div>
            )}

            <div className="pt-4 flex justify-between">
              {isAdding ? (
                <button
                  type="button"
                  onClick={handleAddType}
                  className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <Loader2 size={18} className="animate-spin mr-2" />
                  ) : (
                    <Plus size={18} className="mr-2" />
                  )}
                  Add Payment Type
                </button>
              ) : (
                <>
                  <button
                    type="button"
                    onClick={() => activeType && handleDeleteType(activeType._id)}
                    className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 flex items-center"
                    disabled={isLoading}
                  >
                    <Trash2 size={18} className="mr-2" />
                    Delete
                  </button>
                  <button
                    type="button"
                    onClick={handleUpdateType}
                    className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <Loader2 size={18} className="animate-spin mr-2" />
                    ) : (
                      <Save size={18} className="mr-2" />
                    )}
                    Save Changes
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
