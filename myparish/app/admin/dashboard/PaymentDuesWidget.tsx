'use client';

import { useState, useEffect } from 'react';
import { paymentsApi } from '@/lib/api';
import { Loader2, Al<PERSON><PERSON>riangle, CheckCircle, Clock, Plus, DollarSign } from 'lucide-react';
import Link from 'next/link';

interface DuesSummary {
  total: number;
  paid: number;
  unpaid: number;
  overdue: number;
  partial: number;
}

export default function PaymentDuesWidget() {
  const [summary, setSummary] = useState<DuesSummary>({
    total: 0,
    paid: 0,
    unpaid: 0,
    overdue: 0,
    partial: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    fetchSummary();
  }, []);
  
  const fetchSummary = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const currentYear = new Date().getFullYear();
      const data = await paymentsApi.getSummaryByYear(currentYear);
      
      // Calculate summary
      let totalDues = 0;
      let paidDues = 0;
      let unpaidDues = 0;
      let overdueDues = 0;
      let partialDues = 0;
      
      data.forEach((item: any) => {
        totalDues += item.dueCount;
        paidDues += item.paidCount;
        unpaidDues += item.pendingCount;
        overdueDues += item.overdueCount;
        partialDues += item.partialCount;
      });
      
      setSummary({
        total: totalDues,
        paid: paidDues,
        unpaid: unpaidDues,
        overdue: overdueDues,
        partial: partialDues
      });
    } catch (error) {
      console.error('Failed to fetch dues summary:', error);
      setError('Failed to load dues summary');
    } finally {
      setIsLoading(false);
    }
  };
  
  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-4 flex justify-center items-center h-48">
        <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
        <span className="ml-2">Loading dues summary...</span>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-md p-4 flex flex-col justify-center items-center h-48">
        <AlertTriangle className="h-8 w-8 text-red-500 mb-2" />
        <p className="text-red-500">{error}</p>
      </div>
    );
  }
  
  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">Payment Dues</h2>
        <Link href="/admin/payments/dues" className="text-sm text-blue-600 hover:text-blue-800">
          View All
        </Link>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-blue-50 p-3 rounded-lg border border-blue-100">
          <p className="text-xs text-blue-600">Total Dues</p>
          <p className="text-2xl font-bold">{summary.total}</p>
        </div>
        
        <div className="bg-green-50 p-3 rounded-lg border border-green-100">
          <p className="text-xs text-green-600">Paid</p>
          <div className="flex items-center">
            <p className="text-2xl font-bold">{summary.paid}</p>
            <CheckCircle size={16} className="ml-2 text-green-500" />
          </div>
        </div>
        
        <div className="bg-yellow-50 p-3 rounded-lg border border-yellow-100">
          <p className="text-xs text-yellow-600">Pending</p>
          <div className="flex items-center">
            <p className="text-2xl font-bold">{summary.unpaid}</p>
            <Clock size={16} className="ml-2 text-yellow-500" />
          </div>
        </div>
        
        <div className="bg-red-50 p-3 rounded-lg border border-red-100">
          <p className="text-xs text-red-600">Overdue</p>
          <div className="flex items-center">
            <p className="text-2xl font-bold">{summary.overdue}</p>
            <AlertTriangle size={16} className="ml-2 text-red-500" />
          </div>
        </div>
      </div>
      
      {/* Quick actions */}
      <div className="mt-4 pt-4 border-t border-gray-100 flex justify-between">
        <Link 
          href="/admin/payments/generate-dues" 
          className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
        >
          <Plus size={14} className="mr-1" />
          Generate Dues
        </Link>
        
        <Link 
          href="/admin/payments/record-payment" 
          className="text-sm text-green-600 hover:text-green-800 flex items-center"
        >
          <DollarSign size={14} className="mr-1" />
          Record Payment
        </Link>
      </div>
    </div>
  );
}
