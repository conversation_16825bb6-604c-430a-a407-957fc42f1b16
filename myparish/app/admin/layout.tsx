'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import AdminSidebar from '../components/AdminSidebar';
import MobileSidebar from '../components/MobileSidebar';
import Header from '../components/Header';
import { useAppSelector, useAppDispatch } from '../redux/hooks';
import { restoreAuthState } from '../redux/slices/authSlice';

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { isAuthenticated, user } = useAppSelector(state => state.auth);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  
  // Use a ref to track if redirect has been triggered
  const hasRedirected = useRef(false);
  const authInitialized = useRef(false);
  
  // Ensure auth state is loaded only once
  useEffect(() => {
    if (!authInitialized.current) {
      dispatch(restoreAuthState());
      authInitialized.current = true;
    }
  }, [dispatch]);
  
  // Check if user is authenticated and has admin role
  useEffect(() => {
    // Only check auth after state is loaded and not already redirected
    if (authInitialized.current && !hasRedirected.current) {
      if (!isAuthenticated) {
        console.log('Not authenticated, redirecting to login');
        hasRedirected.current = true;
        router.push('/login');
      } else if (user && user.role !== 'Admin') {
        console.log('Not admin, redirecting to dashboard');
        hasRedirected.current = true;
        router.push('/dashboard');
      }
    }
  }, [isAuthenticated, user, router]);
  
  const toggleMobileSidebar = () => {
    setIsMobileSidebarOpen(!isMobileSidebarOpen);
  };
  
  // If not authenticated, show minimal loading UI
  if (!isAuthenticated) {
    return <div className="flex justify-center items-center min-h-screen">Loading...</div>;
  }
  
  return (
    <div className="flex h-screen bg-gray-100">
      {/* Desktop Sidebar */}
      <AdminSidebar />
      
      {/* Mobile Sidebar */}
      <MobileSidebar 
        isOpen={isMobileSidebarOpen} 
        onClose={() => setIsMobileSidebarOpen(false)} 
      />
      
      <div className="flex-1 flex flex-col overflow-hidden md:ml-64">
        <Header toggleSidebar={toggleMobileSidebar} />
        
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
