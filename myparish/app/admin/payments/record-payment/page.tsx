'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { paymentsApi } from '@/lib/api';
import { useTheme } from '@/app/context/ThemeContext';
import { Loader2, Save, ArrowLeft, Calendar, DollarSign, CreditCard } from 'lucide-react';
import Link from 'next/link';
import { toast } from 'react-toastify';

interface PaymentDue {
  _id: string;
  familyId: {
    _id: string;
    headName: string;
    slNo: string;
  };
  paymentTypeId: {
    _id: string;
    name: string;
  };
  amount: number;
  dueDate: string;
  year: number;
  period?: string;
  isPaid: boolean;
  partialPayment: number;
}

export default function RecordPaymentPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const dueId = searchParams.get('dueId');
  const { colors } = useTheme();
  
  const [due, setDue] = useState<PaymentDue | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  
  const [formData, setFormData] = useState({
    amount: 0,
    paymentDate: new Date().toISOString().split('T')[0],
    receiptNumber: '',
    paymentMethod: 'cash',
    notes: '',
    isPartialPayment: false
  });

  useEffect(() => {
    if (dueId) {
      fetchDue(dueId);
    } else {
      setIsLoading(false);
    }
  }, [dueId]);

  const fetchDue = async (id: string) => {
    try {
      setIsLoading(true);
      const data = await paymentsApi.getDueById(id);
      setDue(data);
      
      // Set default amount (remaining amount if partial payment exists)
      const remainingAmount = data.amount - (data.partialPayment || 0);
      setFormData({
        ...formData,
        amount: remainingAmount
      });
    } catch (error) {
      console.error('Failed to fetch due:', error);
      toast.error('Failed to load payment due details');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type, checked } = e.target as HTMLInputElement;
    
    if (type === 'checkbox') {
      setFormData({
        ...formData,
        [name]: checked
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!due) {
      toast.error('No payment due selected');
      return;
    }
    
    if (formData.amount <= 0) {
      toast.error('Amount must be greater than zero');
      return;
    }
    
    const remainingAmount = due.amount - (due.partialPayment || 0);
    if (formData.amount > remainingAmount && !formData.isPartialPayment) {
      toast.error('Payment amount cannot exceed the remaining due amount');
      return;
    }
    
    try {
      setIsSaving(true);
      
      await paymentsApi.recordPayment({
        dueId: due._id,
        amount: formData.amount,
        paymentDate: formData.paymentDate,
        receiptNumber: formData.receiptNumber,
        paymentMethod: formData.paymentMethod,
        notes: formData.notes,
        isPartialPayment: formData.isPartialPayment
      });
      
      toast.success('Payment recorded successfully');
      router.push('/admin/payments/dues');
    } catch (error) {
      console.error('Failed to record payment:', error);
      toast.error('Failed to record payment');
    } finally {
      setIsSaving(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2">Loading payment due details...</span>
      </div>
    );
  }

  if (!due && dueId) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <p className="text-red-500 font-medium">Payment due not found</p>
        <Link href="/admin/payments/dues" className="mt-4 text-blue-600 hover:underline">
          Return to dues
        </Link>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="flex items-center mb-6">
        <Link href="/admin/payments/dues" className="mr-4">
          <ArrowLeft className="h-5 w-5" />
        </Link>
        <h1 className="text-2xl font-bold">Record Payment</h1>
      </div>

      {due ? (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <h2 className="text-lg font-medium mb-2">Payment Due Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Family</p>
                <p className="font-medium">{due.familyId.headName} ({due.familyId.slNo})</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Payment Type</p>
                <p className="font-medium">{due.paymentTypeId.name}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Amount</p>
                <p className="font-medium">{formatCurrency(due.amount)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Due Date</p>
                <p className="font-medium">{formatDate(due.dueDate)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Year / Period</p>
                <p className="font-medium">{due.year} {due.period && `(${due.period})`}</p>
              </div>
              {due.partialPayment > 0 && (
                <div>
                  <p className="text-sm text-gray-500">Partial Payment Made</p>
                  <p className="font-medium text-green-600">{formatCurrency(due.partialPayment)}</p>
                </div>
              )}
              {due.partialPayment > 0 && (
                <div>
                  <p className="text-sm text-gray-500">Remaining Amount</p>
                  <p className="font-medium text-red-600">{formatCurrency(due.amount - due.partialPayment)}</p>
                </div>
              )}
            </div>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium mb-1" htmlFor="amount">
                  Payment Amount <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <DollarSign size={16} className="text-gray-500" />
                  </div>
                  <input
                    type="number"
                    id="amount"
                    name="amount"
                    value={formData.amount}
                    onChange={handleInputChange}
                    className="w-full pl-10 p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="0.00"
                    required
                    min="0"
                    step="0.01"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1" htmlFor="paymentDate">
                  Payment Date <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Calendar size={16} className="text-gray-500" />
                  </div>
                  <input
                    type="date"
                    id="paymentDate"
                    name="paymentDate"
                    value={formData.paymentDate}
                    onChange={handleInputChange}
                    className="w-full pl-10 p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1" htmlFor="receiptNumber">
                  Receipt Number <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="receiptNumber"
                  name="receiptNumber"
                  value={formData.receiptNumber}
                  onChange={handleInputChange}
                  className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1" htmlFor="paymentMethod">
                  Payment Method <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <CreditCard size={16} className="text-gray-500" />
                  </div>
                  <select
                    id="paymentMethod"
                    name="paymentMethod"
                    value={formData.paymentMethod}
                    onChange={handleInputChange}
                    className="w-full pl-10 p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  >
                    <option value="cash">Cash</option>
                    <option value="check">Check</option>
                    <option value="bank_transfer">Bank Transfer</option>
                    <option value="upi">UPI</option>
                    <option value="card">Card</option>
                    <option value="other">Other</option>
                  </select>
                </div>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium mb-1" htmlFor="notes">
                  Notes
                </label>
                <textarea
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Any additional information about this payment"
                ></textarea>
              </div>

              <div className="md:col-span-2">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isPartialPayment"
                    name="isPartialPayment"
                    checked={formData.isPartialPayment}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isPartialPayment" className="ml-2 block text-sm text-gray-900">
                    This is a partial payment
                  </label>
                </div>
                {formData.isPartialPayment && (
                  <p className="mt-1 text-sm text-gray-500">
                    Marking this as a partial payment will allow additional payments to be made against this due.
                  </p>
                )}
              </div>
            </div>

            <div className="mt-8 flex justify-end">
              <Link
                href="/admin/payments/dues"
                className="px-4 py-2 border rounded text-gray-700 hover:bg-gray-50 mr-2"
              >
                Cancel
              </Link>
              <button
                type="submit"
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center"
                disabled={isSaving}
              >
                {isSaving ? (
                  <Loader2 size={18} className="animate-spin mr-2" />
                ) : (
                  <Save size={18} className="mr-2" />
                )}
                Record Payment
              </button>
            </div>
          </form>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <p className="text-gray-500 mb-4">No payment due selected</p>
          <Link
            href="/admin/payments/dues"
            className="inline-flex items-center text-blue-600 hover:text-blue-800"
          >
            <ArrowLeft size={16} className="mr-1" />
            Return to dues
          </Link>
        </div>
      )}
    </div>
  );
}
