'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { paymentsApi, familiesApi, paymentTypesApi, usersApi } from '@/lib/api';
import { useTheme } from '@/app/context/ThemeContext';
import { Loader2, Save, ArrowLeft, Calendar, DollarSign, User, CreditCard, AlertCircle, CheckCircle } from 'lucide-react';
import Link from 'next/link';
import { toast } from 'react-toastify';

interface Family {
  _id: string;
  headName: string;
  slNo: string;
}

interface PaymentType {
  _id: string;
  name: string;
  amount: number;
  frequency: string;
}

interface User {
  _id: string;
  name: string;
}

interface PaymentDue {
  _id: string;
  familyId: string;
  paymentTypeId: {
    _id: string;
    name: string;
  };
  year: number;
  period?: string;
  amount: number;
  isPaid: boolean;
  partialPayment: number;
  dueDate: string;
}

export default function NewPaymentPage() {
  const router = useRouter();
  const { colors } = useTheme();
  const [families, setFamilies] = useState<Family[]>([]);
  const [paymentTypes, setPaymentTypes] = useState<PaymentType[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [pendingDues, setPendingDues] = useState<PaymentDue[]>([]);
  const [selectedDue, setSelectedDue] = useState<PaymentDue | null>(null);
  const [isPartialPayment, setIsPartialPayment] = useState(false);
  const [remainingAmount, setRemainingAmount] = useState(0);
  const [dueDate, setDueDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  );
  
  const generateReceiptNumber = () => {
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    
    return `RCP-${year}${month}${day}-${random}`;
  };

  const [formData, setFormData] = useState({
    familyId: '',
    paymentTypeId: '',
    amount: 0,
    paymentDate: new Date().toISOString().split('T')[0],
    year: new Date().getFullYear(),
    period: '',
    receiptNumber: generateReceiptNumber(),
    paymentMethod: 'cash',
    collectedBy: '',
    notes: ''
  });

  useEffect(() => {
    Promise.all([
      fetchFamilies(),
      fetchPaymentTypes(),
      fetchUsers()
    ]).then(() => {
      setIsLoading(false);
    });
  }, []);

  const fetchFamilies = async () => {
    try {
      const data = await familiesApi.getAll();
      setFamilies(data);
    } catch (error) {
      console.error('Failed to fetch families:', error);
    }
  };

  const fetchPaymentTypes = async () => {
    try {
      const data = await paymentTypesApi.getAll();
      setPaymentTypes(data);
    } catch (error) {
      console.error('Failed to fetch payment types:', error);
    }
  };

  const fetchUsers = async () => {
    try {
      const data = await usersApi.getAll();
      setUsers(data);
    } catch (error) {
      console.error('Failed to fetch users:', error);
    }
  };

  const fetchPendingDues = async (familyId: string) => {
    try {
      const data = await paymentsApi.getPendingByFamily(familyId);
      setPendingDues(data);
    } catch (error) {
      console.error('Failed to fetch pending dues:', error);
      setPendingDues([]);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    if (name === 'familyId' && value) {
      fetchPendingDues(value);
      setSelectedDue(null);
      setFormData({
        ...formData,
        familyId: value,
        paymentTypeId: '',
        amount: 0,
        year: new Date().getFullYear(),
        period: ''
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handlePaymentTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const typeId = e.target.value;
    const selectedType = paymentTypes.find(type => type._id === typeId);
    
    if (selectedType) {
      setFormData({
        ...formData,
        paymentTypeId: typeId,
        amount: selectedType.amount
      });
      setRemainingAmount(selectedType.amount);
      
      // Reset selected due when payment type changes
      setSelectedDue(null);
      setIsPartialPayment(false);
    } else {
      setFormData({
        ...formData,
        paymentTypeId: '',
        amount: 0
      });
    }
  };

  const handleDueSelection = (due: PaymentDue) => {
    setSelectedDue(due);
    
    // Calculate remaining amount
    const remainingAmount = due.amount - due.partialPayment;
    
    setFormData({
      ...formData,
      paymentTypeId: due.paymentTypeId._id,
      amount: remainingAmount,
      year: due.year,
      period: due.period || '',
      notes: `Payment for ${due.paymentTypeId.name} - ${due.year}${due.period ? ` (${due.period})` : ''}`
    });
  };

  const handlePartialPaymentToggle = () => {
    setIsPartialPayment(!isPartialPayment);
    
    if (!isPartialPayment) {
      // When enabling partial payment, reset amount to allow user input
      setFormData({
        ...formData,
        amount: 0
      });
    } else {
      // When disabling partial payment, set amount back to full amount
      const selectedType = paymentTypes.find(type => type._id === formData.paymentTypeId);
      if (selectedType) {
        setFormData({
          ...formData,
          amount: selectedType.amount
        });
      }
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2">Loading...</span>
      </div>
    );
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.familyId) {
      toast.error('Please select a family');
      return;
    }
    
    if (!formData.paymentTypeId) {
      toast.error('Please select a payment type');
      return;
    }
    
    if (formData.amount <= 0) {
      toast.error('Amount must be greater than zero');
      return;
    }
    
    try {
      setIsSaving(true);
      
      // If it's a partial payment and not for an existing due, create a due for the remaining amount
      if (isPartialPayment && !selectedDue) {
        const selectedType = paymentTypes.find(type => type._id === formData.paymentTypeId);
        if (selectedType && formData.amount < selectedType.amount) {
          const remainingAmount = selectedType.amount - formData.amount;
          
          // Create payment with partial payment flag
          const newPayment = await paymentsApi.create({
            ...formData,
            isPartialPayment: true,
            createDueForRemaining: true,
            remainingAmount: remainingAmount,
            dueDate: dueDate,
            // Only include period if it's not empty
            ...(formData.period ? { period: formData.period } : {})
          });
          
          toast.success('Payment recorded successfully and due created for remaining amount');
          router.push(`/admin/payments/receipt/${newPayment._id}`);
          return;
        }
      }
      
      // Regular payment processing
      const newPayment = await paymentsApi.create(formData);
      
      toast.success('Payment recorded successfully');
      router.push(`/admin/payments/receipt/${newPayment._id}`);
    } catch (error) {
      console.error('Failed to create payment:', error);
      toast.error('Failed to record payment');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="p-4">
      <div className="flex items-center mb-6">
        <Link href="/admin/payments" className="mr-4">
          <ArrowLeft className="h-5 w-5" />
        </Link>
        <h1 className="text-2xl font-bold">Record New Payment</h1>
      </div>

      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-md p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium mb-1" htmlFor="familyId">
              Family <span className="text-red-500">*</span>
            </label>
            <select
              id="familyId"
              name="familyId"
              value={formData.familyId}
              onChange={handleInputChange}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            >
              <option value="">Select Family</option>
              {families.map(family => (
                <option key={family._id} value={family._id}>
                  {family.slNo} - {family.headName}
                </option>
              ))}
            </select>
          </div>

          {formData.familyId && pendingDues.length > 0 && (
            <div className="md:col-span-2">
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-4">
                <h3 className="text-blue-800 font-medium mb-2 flex items-center">
                  <AlertCircle size={18} className="mr-2" />
                  Pending Dues for this Family
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {pendingDues.map(due => (
                    <div 
                      key={due._id}
                      onClick={() => handleDueSelection(due)}
                      className={`p-3 rounded-lg border cursor-pointer transition-all ${
                        selectedDue?._id === due._id 
                          ? 'bg-blue-100 border-blue-400' 
                          : 'bg-white border-gray-200 hover:border-blue-300'
                      }`}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium">{due.paymentTypeId.name}</p>
                          <p className="text-sm text-gray-600">
                            {due.year} {due.period && `(${due.period})`}
                          </p>
                          <p className="text-sm">
                            Due: {formatCurrency(due.amount)}
                          </p>
                          {due.partialPayment > 0 && (
                            <p className="text-sm text-orange-600">
                              Paid: {formatCurrency(due.partialPayment)} | 
                              Remaining: {formatCurrency(due.amount - due.partialPayment)}
                            </p>
                          )}
                        </div>
                        {selectedDue?._id === due._id && (
                          <CheckCircle size={18} className="text-blue-600" />
                        )}
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        Due Date: {formatDate(due.dueDate)}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium mb-1" htmlFor="paymentTypeId">
              Payment Type <span className="text-red-500">*</span>
            </label>
            <select
              id="paymentTypeId"
              name="paymentTypeId"
              value={formData.paymentTypeId}
              onChange={handlePaymentTypeChange}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
              disabled={!!selectedDue}
            >
              <option value="">Select Payment Type</option>
              {paymentTypes.map(type => (
                <option key={type._id} value={type._id}>
                  {type.name} - {formatCurrency(type.amount)}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1" htmlFor="amount">
              Amount <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <DollarSign size={16} className="text-gray-500" />
              </div>
              <input
                type="number"
                id="amount"
                name="amount"
                value={formData.amount}
                onChange={handleInputChange}
                className="w-full pl-10 p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="0.00"
                required
                min="0"
                step="0.01"
                disabled={!!selectedDue && !isPartialPayment}
              />
            </div>
          </div>

          {formData.paymentTypeId && !selectedDue && (
            <div className="md:col-span-2">
              <label className="inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="form-checkbox h-5 w-5 text-blue-600" 
                  checked={isPartialPayment}
                  onChange={handlePartialPaymentToggle}
                />
                <span className="ml-2 text-gray-700">Make partial payment</span>
              </label>
              {isPartialPayment && (
                <p className="text-sm text-gray-600 mt-1">
                  Enter the amount you wish to pay. The remaining balance will be created as a due.
                </p>
              )}
            </div>
          )}

          {isPartialPayment && (
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700">
                Due Date for Remaining Amount
              </label>
              <div className="mt-1 relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Calendar className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="date"
                  name="dueDate"
                  value={dueDate}
                  onChange={(e) => setDueDate(e.target.value)}
                  className="focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md"
                  required
                />
              </div>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium mb-1" htmlFor="paymentDate">
              Payment Date <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Calendar size={16} className="text-gray-500" />
              </div>
              <input
                type="date"
                id="paymentDate"
                name="paymentDate"
                value={formData.paymentDate}
                onChange={handleInputChange}
                className="w-full pl-10 p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1" htmlFor="year">
              Year <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              id="year"
              name="year"
              value={formData.year}
              onChange={handleInputChange}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
              min="2000"
              max="2100"
              disabled={!!selectedDue}
            />
          </div>

          {isPartialPayment && (
            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="period">
                Period
              </label>
              <select
                id="period"
                name="period"
                value={formData.period}
                onChange={handleInputChange}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select Period (Optional)</option>
                <option value="Q1">Q1</option>
                <option value="Q2">Q2</option>
                <option value="Q3">Q3</option>
                <option value="Q4">Q4</option>
                <option value="Jan">January</option>
                <option value="Feb">February</option>
                <option value="Mar">March</option>
                <option value="Apr">April</option>
                <option value="May">May</option>
                <option value="Jun">June</option>
                <option value="Jul">July</option>
                <option value="Aug">August</option>
                <option value="Sep">September</option>
                <option value="Oct">October</option>
                <option value="Nov">November</option>
                <option value="Dec">December</option>
              </select>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium mb-1" htmlFor="receiptNumber">
              Receipt Number <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="receiptNumber"
              name="receiptNumber"
              value={formData.receiptNumber}
              onChange={handleInputChange}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1" htmlFor="paymentMethod">
              Payment Method <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <CreditCard size={16} className="text-gray-500" />
              </div>
              <select
                id="paymentMethod"
                name="paymentMethod"
                value={formData.paymentMethod}
                onChange={handleInputChange}
                className="w-full pl-10 p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value="cash">Cash</option>
                <option value="check">Check</option>
                <option value="bank_transfer">Bank Transfer</option>
                <option value="online">Online Payment</option>
                <option value="other">Other</option>
              </select>
            </div>
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium mb-1" htmlFor="notes">
              Notes
            </label>
            <textarea
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleInputChange}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              rows={3}
              placeholder="Add any additional notes here..."
            />
          </div>
        </div>

        <div className="mt-6 flex justify-end">
          <Link
            href="/admin/payments"
            className="px-4 py-2 border rounded text-gray-700 hover:bg-gray-50 mr-2"
          >
            Cancel
          </Link>
          <button
            type="submit"
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center"
            disabled={isSaving}
          >
            {isSaving ? (
              <Loader2 size={18} className="animate-spin mr-2" />
            ) : (
              <Save size={18} className="mr-2" />
            )}
            Record Payment
          </button>
        </div>
      </form>
    </div>
  );
}
