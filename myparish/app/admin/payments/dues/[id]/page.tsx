'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { paymentsApi } from '@/lib/api';
import { useTheme } from '@/app/context/ThemeContext';
import { 
  Loader2, ArrowLeft, Calendar, DollarSign, 
  CheckCircle, XCircle, Clock, AlertTriangle, 
  FileText, Printer, ArrowRight
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'react-toastify';

interface PaymentDue {
  _id: string;
  familyId: {
    _id: string;
    headName: string;
    slNo: string;
  };
  paymentTypeId: {
    _id: string;
    name: string;
  };
  amount: number;
  dueDate: string;
  year: number;
  period?: string;
  isPaid: boolean;
  partialPayment: number;
  createdAt: string;
  payments: Array<{
    _id: string;
    amount: number;
    paymentDate: string;
    receiptNumber: string;
    paymentMethod: string;
    notes?: string;
    createdAt: string;
  }>;
}

export default function PaymentDueDetailsPage() {
  const router = useRouter();
  const params = useParams();
  const id = params.id as string;
  const { colors } = useTheme();
  
  const [due, setDue] = useState<PaymentDue | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    if (id) {
      fetchDue(id);
    }
  }, [id]);

  const fetchDue = async (dueId: string) => {
    try {
      setIsLoading(true);
      const data = await paymentsApi.getDueById(dueId);
      setDue(data);
    } catch (error) {
      console.error('Failed to fetch due:', error);
      toast.error('Failed to load payment due details');
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getPaymentMethodLabel = (method: string) => {
    const methods: Record<string, string> = {
      cash: 'Cash',
      check: 'Check',
      bank_transfer: 'Bank Transfer',
      upi: 'UPI',
      card: 'Card',
      other: 'Other'
    };
    return methods[method] || method;
  };

  const recordPayment = () => {
    router.push(`/admin/payments/record-payment?dueId=${id}`);
  };

  const printReceipt = (paymentId: string) => {
    // Implementation for printing receipt
    toast.info('Print functionality will be implemented soon');
  };

  const calculateRemainingAmount = () => {
    if (!due) return 0;
    
    const totalPaid = due.partialPayment || 0;
    return due.amount - totalPaid;
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2">Loading payment due details...</span>
      </div>
    );
  }

  if (!due) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <p className="text-red-500 font-medium">Payment due not found</p>
        <Link href="/admin/payments/dues" className="mt-4 text-blue-600 hover:underline">
          Return to dues
        </Link>
      </div>
    );
  }

  const remainingAmount = calculateRemainingAmount();

  return (
    <div className="p-4">
      <div className="flex items-center mb-6">
        <Link href="/admin/payments/dues" className="mr-4">
          <ArrowLeft className="h-5 w-5" />
        </Link>
        <h1 className="text-2xl font-bold">Payment Due Details</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <div className="flex justify-between items-start mb-6">
              <h2 className="text-xl font-semibold">Due Information</h2>
              <div className="flex items-center">
                {due.isPaid ? (
                  <>
                    <CheckCircle size={20} className="text-green-500 mr-2" />
                    <span className="text-green-600 font-medium">Paid</span>
                  </>
                ) : due.partialPayment && due.partialPayment > 0 ? (
                  <>
                    <Clock size={20} className="text-blue-500 mr-2" />
                    <span className="text-blue-600 font-medium">Partially Paid</span>
                    <span className="ml-2 text-sm text-gray-500">
                      ({(due.partialPayment / due.amount * 100).toFixed(0)}%)
                    </span>
                  </>
                ) : new Date(due.dueDate) < new Date() ? (
                  <>
                    <AlertTriangle size={20} className="text-red-500 mr-2" />
                    <span className="text-red-600 font-medium">Overdue</span>
                  </>
                ) : (
                  <>
                    <Clock size={20} className="text-yellow-500 mr-2" />
                    <span className="text-yellow-600 font-medium">Pending</span>
                  </>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <p className="text-sm text-gray-500">Family</p>
                <p className="font-medium text-lg">{due.familyId.headName}</p>
                <p className="text-sm text-gray-500">Family ID: {due.familyId.slNo}</p>
              </div>
              
              <div>
                <p className="text-sm text-gray-500">Payment Type</p>
                <p className="font-medium text-lg">{due.paymentTypeId.name}</p>
                <p className="text-sm text-gray-500">Year: {due.year} {due.period && `(${due.period})`}</p>
              </div>
              
              <div>
                <p className="text-sm text-gray-500">Total Amount</p>
                <p className="font-medium text-lg">{formatCurrency(due.amount)}</p>
              </div>
              
              <div>
                <p className="text-sm text-gray-500">Due Date</p>
                <p className="font-medium text-lg">{formatDate(due.dueDate)}</p>
              </div>
              
              <div>
                <p className="text-sm text-gray-500">Created On</p>
                <p className="font-medium">{formatDateTime(due.createdAt)}</p>
              </div>
              
              {due.partialPayment > 0 && (
                <div>
                  <p className="text-sm text-gray-500">Partial Payment Made</p>
                  <p className="font-medium text-green-600">{formatCurrency(due.partialPayment)}</p>
                </div>
              )}
              
              {!due.isPaid && (
                <div>
                  <p className="text-sm text-gray-500">Remaining Amount</p>
                  <p className="font-medium text-red-600">{formatCurrency(remainingAmount)}</p>
                </div>
              )}
            </div>

            {!due.isPaid && (
              <div className="mt-6 flex justify-end">
                <button
                  onClick={recordPayment}
                  className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center"
                >
                  <DollarSign size={18} className="mr-2" />
                  Record Payment
                </button>
              </div>
            )}
          </div>

          {due.payments && due.payments.length > 0 ? (
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-6">Payment History</h2>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Receipt #
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Method
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {due.payments.map(payment => (
                      <tr key={payment._id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm font-medium text-gray-900">
                            {payment.receiptNumber}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm text-gray-500">
                            {formatDate(payment.paymentDate)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm font-medium text-gray-900">
                            {formatCurrency(payment.amount)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm text-gray-500">
                            {getPaymentMethodLabel(payment.paymentMethod)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <button
                            onClick={() => printReceipt(payment._id)}
                            className="text-blue-600 hover:text-blue-900 flex items-center"
                          >
                            <Printer size={14} className="mr-1" /> Print
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-md p-6 text-center">
              <h2 className="text-xl font-semibold mb-4">Payment History</h2>
              <p className="text-gray-500">No payments recorded yet</p>
              {!due.isPaid && (
                <button
                  onClick={recordPayment}
                  className="mt-4 inline-flex items-center text-blue-600 hover:text-blue-800"
                >
                  <DollarSign size={16} className="mr-1" />
                  Record a payment
                </button>
              )}
            </div>
          )}
        </div>

        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Payment Summary</h2>
            <div className="space-y-4">
              <div className="flex justify-between items-center pb-2 border-b border-gray-100">
                <span className="text-gray-600">Total Due Amount</span>
                <span className="font-medium">{formatCurrency(due.amount)}</span>
              </div>
              
              <div className="flex justify-between items-center pb-2 border-b border-gray-100">
                <span className="text-gray-600">Total Paid</span>
                <span className="font-medium text-green-600">{formatCurrency(due.partialPayment || 0)}</span>
              </div>
              
              {!due.isPaid && (
                <div className="flex justify-between items-center pb-2 border-b border-gray-100">
                  <span className="text-gray-600">Remaining</span>
                  <span className="font-medium text-red-600">{formatCurrency(remainingAmount)}</span>
                </div>
              )}
              
              <div className="flex justify-between items-center pt-2">
                <span className="text-gray-600 font-medium">Status</span>
                {due.isPaid ? (
                  <span className="text-green-600 font-medium">Fully Paid</span>
                ) : due.partialPayment > 0 ? (
                  <span className="text-yellow-600 font-medium">Partially Paid</span>
                ) : (
                  <span className="text-red-600 font-medium">Unpaid</span>
                )}
              </div>
            </div>
            
            {!due.isPaid && (
              <div className="mt-6">
                <button
                  onClick={recordPayment}
                  className="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center justify-center"
                >
                  <DollarSign size={18} className="mr-2" />
                  Record Payment
                </button>
              </div>
            )}
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Actions</h2>
            <div className="space-y-3">
              <Link
                href={`/admin/families/${due.familyId._id}`}
                className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded hover:bg-gray-200 flex items-center"
              >
                <FileText size={18} className="mr-2" />
                View Family Details
              </Link>
              
              <button
                onClick={() => router.push('/admin/payments/dues')}
                className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded hover:bg-gray-200 flex items-center"
              >
                <ArrowLeft size={18} className="mr-2" />
                Back to Dues List
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
