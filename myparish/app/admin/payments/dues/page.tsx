'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { paymentsApi, paymentTypesApi, familiesApi } from '@/lib/api';
import { useTheme } from '@/app/context/ThemeContext';
import { 
  Loader2, Plus, Filter, Search, DollarSign, 
  CheckCircle, XCircle, Clock, AlertTriangle, 
  Download, RefreshCw, Eye
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'react-toastify';

interface PaymentDue {
  _id: string;
  familyId: {
    _id: string;
    headName: string;
    slNo: string;
  };
  paymentTypeId: {
    _id: string;
    name: string;
  };
  amount: number;
  dueDate: string;
  year: number;
  period?: string;
  isPaid: boolean;
  partialPayment: number;
}

interface PaymentType {
  _id: string;
  name: string;
}

interface Family {
  _id: string;
  headName: string;
  slNo: string;
}

interface PaginationInfo {
  total: number;
  page: number;
  pages: number;
}

export default function PaymentDuesPage() {
  const router = useRouter();
  const { colors } = useTheme();
  
  const [dues, setDues] = useState<PaymentDue[]>([]);
  const [paymentTypes, setPaymentTypes] = useState<PaymentType[]>([]);
  const [families, setFamilies] = useState<Family[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    page: 1,
    pages: 1
  });
  
  const [filters, setFilters] = useState({
    familyId: '',
    paymentTypeId: '',
    year: new Date().getFullYear().toString(),
    status: '',
    startDate: '',
    endDate: '',
    search: ''
  });
  
  const [showFilters, setShowFilters] = useState(false);
  
  useEffect(() => {
    fetchData();
  }, [pagination.page, filters]);
  
  const fetchData = async () => {
    try {
      setIsLoading(true);
      
      // Fetch payment types and families for filters
      if (paymentTypes.length === 0) {
        const typesData = await paymentTypesApi.getAll();
        setPaymentTypes(typesData);
      }
      
      if (families.length === 0) {
        const familiesData = await familiesApi.getAll();
        setFamilies(familiesData);
      }
      
      // Fetch dues with filters
      const params = {
        page: pagination.page,
        limit: 20,
        ...filters
      };
      
      const data = await paymentsApi.getAllDues(params);
      setDues(data.dues);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Failed to fetch payment dues:', error);
      toast.error('Failed to load payment dues');
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilters({
      ...filters,
      [name]: value
    });
    
    // Reset to first page when filters change
    if (pagination.page !== 1) {
      setPagination({
        ...pagination,
        page: 1
      });
    }
  };
  
  const clearFilters = () => {
    setFilters({
      familyId: '',
      paymentTypeId: '',
      year: new Date().getFullYear().toString(),
      status: '',
      startDate: '',
      endDate: '',
      search: ''
    });
  };
  
  const handlePageChange = (newPage: number) => {
    if (newPage > 0 && newPage <= pagination.pages) {
      setPagination({
        ...pagination,
        page: newPage
      });
    }
  };
  
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };
  
  const getStatusBadge = (due: PaymentDue) => {
    if (due.isPaid) {
      return (
        <span className="px-2 py-1 inline-flex text-xs font-semibold rounded-full bg-green-100 text-green-800">
          <CheckCircle size={12} className="mr-1" /> Paid
        </span>
      );
    } else if (due.partialPayment > 0) {
      return (
        <span className="px-2 py-1 inline-flex text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
          <Clock size={12} className="mr-1" /> Partial
        </span>
      );
    } else if (new Date(due.dueDate) < new Date()) {
      return (
        <span className="px-2 py-1 inline-flex text-xs font-semibold rounded-full bg-red-100 text-red-800">
          <AlertTriangle size={12} className="mr-1" /> Overdue
        </span>
      );
    } else {
      return (
        <span className="px-2 py-1 inline-flex text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
          <Clock size={12} className="mr-1" /> Pending
        </span>
      );
    }
  };
  
  const exportToCSV = () => {
    // Implementation for exporting to CSV
    toast.info('Export functionality will be implemented soon');
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Reset to first page when searching
    setPagination({
      ...pagination,
      page: 1
    });
    // Fetch data with current filters
    fetchData();
  };
  
  return (
    <div className="p-4">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <h1 className="text-2xl font-bold mb-4 md:mb-0">Payment Dues</h1>
        
        <div className="flex flex-col sm:flex-row gap-2">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center justify-center px-4 py-2 border rounded text-gray-700 hover:bg-gray-50"
          >
            <Filter size={16} className="mr-2" />
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </button>
          
          <button
            onClick={exportToCSV}
            className="flex items-center justify-center px-4 py-2 border rounded text-gray-700 hover:bg-gray-50"
          >
            <Download size={16} className="mr-2" />
            Export
          </button>
          
          <Link
            href="/admin/payments/generate-dues"
            className="flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            <Plus size={16} className="mr-2" />
            Generate Dues
          </Link>
        </div>
      </div>
      
      {showFilters && (
        <div className="bg-white rounded-lg shadow-md p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="familyId">
                Family
              </label>
              <select
                id="familyId"
                name="familyId"
                value={filters.familyId}
                onChange={handleFilterChange}
                className="w-full p-2 border rounded"
              >
                <option value="">All Families</option>
                {families.map(family => (
                  <option key={family._id} value={family._id}>
                    {family.headName} ({family.slNo})
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="paymentTypeId">
                Payment Type
              </label>
              <select
                id="paymentTypeId"
                name="paymentTypeId"
                value={filters.paymentTypeId}
                onChange={handleFilterChange}
                className="w-full p-2 border rounded"
              >
                <option value="">All Types</option>
                {paymentTypes.map(type => (
                  <option key={type._id} value={type._id}>
                    {type.name}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="year">
                Year
              </label>
              <select
                id="year"
                name="year"
                value={filters.year}
                onChange={handleFilterChange}
                className="w-full p-2 border rounded"
              >
                {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - i).map(year => (
                  <option key={year} value={year}>
                    {year}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="status">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={filters.status}
                onChange={handleFilterChange}
                className="w-full p-2 border rounded"
              >
                <option value="">All Statuses</option>
                <option value="paid">Paid</option>
                <option value="unpaid">Unpaid</option>
                <option value="partial">Partially Paid</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="startDate">
                Due Date From
              </label>
              <input
                type="date"
                id="startDate"
                name="startDate"
                value={filters.startDate}
                onChange={handleFilterChange}
                className="w-full p-2 border rounded"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="endDate">
                Due Date To
              </label>
              <input
                type="date"
                id="endDate"
                name="endDate"
                value={filters.endDate}
                onChange={handleFilterChange}
                className="w-full p-2 border rounded"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="search">
                Search
              </label>
              <form onSubmit={handleSearch} className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search size={16} className="text-gray-500" />
                </div>
                <input
                  type="text"
                  id="search"
                  name="search"
                  value={filters.search}
                  onChange={handleFilterChange}
                  placeholder="Search by family name, ID..."
                  className="w-full pl-10 p-2 border rounded"
                />
                <button type="submit" className="sr-only">Search</button>
              </form>
            </div>
            
            <div className="flex items-end">
              <button
                onClick={clearFilters}
                className="px-4 py-2 border rounded text-gray-700 hover:bg-gray-50 flex items-center"
              >
                <RefreshCw size={16} className="mr-2" />
                Reset Filters
              </button>
            </div>
          </div>
        </div>
      )}
      
      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-2">Loading payment dues...</span>
        </div>
      ) : dues.length === 0 ? (
        <div className="p-8 text-center">
          <p className="text-gray-500 mb-4">No payment dues found</p>
          <Link
            href="/admin/payments/generate-dues"
            className="inline-flex items-center text-blue-600 hover:text-blue-800"
          >
            <Plus size={16} className="mr-1" />
            Generate New Dues
          </Link>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Family
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Payment Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Due Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {dues.map(due => (
                <tr key={due._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="text-sm font-medium text-gray-900">
                        {due.familyId.headName}
                      </div>
                      <div className="ml-1 text-sm text-gray-500">
                        ({due.familyId.slNo})
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-900">
                      {due.paymentTypeId.name}
                    </span>
                    <div className="text-xs text-gray-500">
                      {due.year} {due.period && `(${due.period})`}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm font-medium text-gray-900">
                      {formatCurrency(due.amount)}
                    </span>
                    {due.partialPayment > 0 && !due.isPaid && (
                      <div className="text-xs text-green-600">
                        Partial: {formatCurrency(due.partialPayment)}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-500">
                      {formatDate(due.dueDate)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(due)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex space-x-2">
                      <Link
                        href={`/admin/payments/dues/${due._id}`}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <Eye size={14} className="inline mr-1" /> View
                      </Link>
                      {!due.isPaid && (
                        <Link
                          href={`/admin/payments/record-payment?dueId=${due._id}`}
                          className="text-green-600 hover:text-green-900 flex items-center"
                        >
                          <DollarSign size={14} className="mr-1" /> Pay
                        </Link>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      
      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="px-6 py-4 flex items-center justify-between border-t border-gray-200 mt-4">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page === 1}
              className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                pagination.page === 1
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              Previous
            </button>
            <button
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page === pagination.pages}
              className={`ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                pagination.page === pagination.pages
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">{(pagination.page - 1) * 20 + 1}</span> to{' '}
                <span className="font-medium">
                  {Math.min(pagination.page * 20, pagination.total)}
                </span>{' '}
                of <span className="font-medium">{pagination.total}</span> results
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page === 1}
                  className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${
                    pagination.page === 1
                      ? 'text-gray-300 cursor-not-allowed'
                      : 'text-gray-500 hover:bg-gray-50'
                  }`}
                >
                  <span className="sr-only">Previous</span>
                  <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </button>
                
                {/* Page numbers - show max 5 pages */}
                {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                  // Calculate page numbers to show
                  let pageNum;
                  if (pagination.pages <= 5) {
                    // If 5 or fewer pages, show all
                    pageNum = i + 1;
                  } else if (pagination.page <= 3) {
                    // If current page is near start
                    pageNum = i + 1;
                  } else if (pagination.page >= pagination.pages - 2) {
                    // If current page is near end
                    pageNum = pagination.pages - 4 + i;
                  } else {
                    // Current page is in middle
                    pageNum = pagination.page - 2 + i;
                  }
                  
                  return (
                    <button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        pageNum === pagination.page
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
                
                <button
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page === pagination.pages}
                  className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                    pagination.page === pagination.pages
                      ? 'text-gray-300 cursor-not-allowed'
                      : 'text-gray-500 hover:bg-gray-50'
                  }`}
                >
                  <span className="sr-only">Next</span>
                  <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
