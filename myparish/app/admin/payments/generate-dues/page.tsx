'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { paymentsApi, paymentTypesApi, familiesApi } from '@/lib/api';
import { useTheme } from '@/app/context/ThemeContext';
import { 
  Loader2, Save, ArrowLeft, Calendar, Users, 
  CheckCircle, AlertCircle, Filter, Download 
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'react-toastify';

interface PaymentType {
  _id: string;
  name: string;
  amount: number;
  frequency: string;
}

interface Family {
  _id: string;
  headName: string;
  slNo: string;
}

interface GeneratedDue {
  familyId: {
    _id: string;
    headName: string;
    slNo: string;
  };
  amount: number;
  dueDate: string;
  status: 'new' | 'existing';
}

export default function GenerateDuesPage() {
  const router = useRouter();
  const { colors } = useTheme();
  const [paymentTypes, setPaymentTypes] = useState<PaymentType[]>([]);
  const [families, setFamilies] = useState<Family[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedDues, setGeneratedDues] = useState<GeneratedDue[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  
  const [formData, setFormData] = useState({
    paymentTypeId: '',
    year: new Date().getFullYear(),
    dueDate: new Date().toISOString().split('T')[0],
    familyFilter: '',
    wardFilter: ''
  });

  useEffect(() => {
    Promise.all([
      fetchPaymentTypes(),
      fetchFamilies()
    ]).then(() => {
      setIsLoading(false);
    });
  }, []);

  const fetchPaymentTypes = async () => {
    try {
      const data = await paymentTypesApi.getAll();
      setPaymentTypes(data);
    } catch (error) {
      console.error('Failed to fetch payment types:', error);
      toast.error('Failed to load payment types');
    }
  };

  const fetchFamilies = async () => {
    try {
      const data = await familiesApi.getAll();
      setFamilies(data);
    } catch (error) {
      console.error('Failed to fetch families:', error);
      toast.error('Failed to load families');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const previewDues = async () => {
    // Validate required fields
    if (!formData.paymentTypeId) {
      toast.error('Please select a payment type');
      return;
    }
    
    if (!formData.year) {
      toast.error('Please enter a valid year');
      return;
    }
    
    if (!formData.dueDate) {
      toast.error('Please select a due date');
      return;
    }
    
    try {
      setIsGenerating(true);
      console.log('Preview dues payload:', {
        paymentTypeId: formData.paymentTypeId,
        year: Number(formData.year),
        dueDate: formData.dueDate,
        familyFilter: formData.familyFilter || undefined,
        wardFilter: formData.wardFilter || undefined
      });
      
      // Preview dues generation
      const data = await paymentsApi.previewDues({
        paymentTypeId: formData.paymentTypeId,
        year: Number(formData.year),
        dueDate: formData.dueDate,
        familyFilter: formData.familyFilter || undefined,
        wardFilter: formData.wardFilter || undefined
      });
      
      setGeneratedDues(data);
      setShowPreview(true);
    } catch (error: any) {
      console.error('Failed to preview dues:', error);
      // Show more detailed error message
      if (error.response) {
        console.error('Error response:', error.response);
        const errorMsg = error.response.data?.message || 'Unknown error';
        toast.error(`Failed to preview dues: ${errorMsg}`);
      } else {
        toast.error('Failed to preview dues');
      }
    } finally {
      setIsGenerating(false);
    }
  };

  const generateDues = async () => {
    // Validate required fields
    if (!formData.paymentTypeId) {
      toast.error('Please select a payment type');
      return;
    }
    
    if (!formData.year) {
      toast.error('Please enter a valid year');
      return;
    }
    
    if (!formData.dueDate) {
      toast.error('Please select a due date');
      return;
    }
    
    try {
      setIsGenerating(true);
      console.log('Generate dues payload:', {
        paymentTypeId: formData.paymentTypeId,
        year: Number(formData.year),
        dueDate: formData.dueDate,
        familyFilter: formData.familyFilter || undefined,
        wardFilter: formData.wardFilter || undefined
      });
      
      // Generate dues
      const data = await paymentsApi.generateDues({
        paymentTypeId: formData.paymentTypeId,
        year: Number(formData.year),
        dueDate: formData.dueDate,
        familyFilter: formData.familyFilter || undefined,
        wardFilter: formData.wardFilter || undefined
      });
      
      toast.success(`Successfully generated ${data.length} dues`);
      router.push('/admin/payments/dues');
    } catch (error: any) {
      console.error('Failed to generate dues:', error);
      // Show more detailed error message
      if (error.response) {
        console.error('Error response:', error.response);
        const errorMsg = error.response.data?.message || 'Unknown error';
        toast.error(`Failed to generate dues: ${errorMsg}`);
      } else {
        toast.error('Failed to generate dues');
      }
    } finally {
      setIsGenerating(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const exportDuesPreview = () => {
    // Implementation for exporting dues preview
    toast.info('Export functionality will be implemented soon');
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2">Loading...</span>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="flex items-center mb-6">
        <Link href="/admin/payments" className="mr-4">
          <ArrowLeft className="h-5 w-5" />
        </Link>
        <h1 className="text-2xl font-bold">Generate Payment Dues</h1>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium mb-1" htmlFor="paymentTypeId">
              Payment Type <span className="text-red-500">*</span>
            </label>
            <select
              id="paymentTypeId"
              name="paymentTypeId"
              value={formData.paymentTypeId}
              onChange={handleInputChange}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            >
              <option value="">Select Payment Type</option>
              {paymentTypes.map(type => (
                <option key={type._id} value={type._id}>
                  {type.name} - {formatCurrency(type.amount)} ({type.frequency})
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1" htmlFor="year">
              Year <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              id="year"
              name="year"
              value={formData.year}
              onChange={handleInputChange}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
              min="2000"
              max="2100"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1" htmlFor="dueDate">
              Due Date <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Calendar size={16} className="text-gray-500" />
              </div>
              <input
                type="date"
                id="dueDate"
                name="dueDate"
                value={formData.dueDate}
                onChange={handleInputChange}
                className="w-full pl-10 p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1" htmlFor="familyFilter">
              Family Filter (Optional)
            </label>
            <select
              id="familyFilter"
              name="familyFilter"
              value={formData.familyFilter}
              onChange={handleInputChange}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Families</option>
              {families.map(family => (
                <option key={family._id} value={family._id}>
                  {family.slNo} - {family.headName}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1" htmlFor="wardFilter">
              Ward Filter (Optional)
            </label>
            <input
              type="text"
              id="wardFilter"
              name="wardFilter"
              value={formData.wardFilter}
              onChange={handleInputChange}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter ward number"
            />
          </div>
        </div>

        <div className="mt-6 flex justify-end">
          <button
            type="button"
            onClick={previewDues}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 flex items-center mr-2"
            disabled={isGenerating}
          >
            {isGenerating ? (
              <Loader2 size={18} className="animate-spin mr-2" />
            ) : (
              <Filter size={18} className="mr-2" />
            )}
            Preview Dues
          </button>
          <button
            type="button"
            onClick={generateDues}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center"
            disabled={isGenerating}
          >
            {isGenerating ? (
              <Loader2 size={18} className="animate-spin mr-2" />
            ) : (
              <Save size={18} className="mr-2" />
            )}
            Generate Dues
          </button>
        </div>
      </div>

      {/* Preview Section */}
      {showPreview && (
        <div className="mt-8 bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Preview of Dues to Generate</h2>
            <button
              onClick={exportDuesPreview}
              className="bg-gray-100 text-gray-700 px-3 py-2 rounded hover:bg-gray-200 flex items-center"
            >
              <Download size={18} className="mr-2" />
              Export
            </button>
          </div>
          
          <div className="mb-4 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
              <h3 className="text-blue-800 text-sm font-medium mb-2">Total Dues</h3>
              <p className="text-2xl font-bold text-blue-900">{generatedDues.length}</p>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg border border-green-100">
              <h3 className="text-green-800 text-sm font-medium mb-2">Total Amount</h3>
              <p className="text-2xl font-bold text-green-900">
                {formatCurrency(generatedDues.reduce((sum, due) => sum + due.amount, 0))}
              </p>
            </div>
            
            <div className="bg-orange-50 p-4 rounded-lg border border-orange-100">
              <h3 className="text-orange-800 text-sm font-medium mb-2">Due Date</h3>
              <p className="text-2xl font-bold text-orange-900">
                {formatDate(formData.dueDate)}
              </p>
            </div>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Family
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Due Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {generatedDues.map((due, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="text-sm font-medium text-gray-900">
                          {due.familyId.headName}
                        </div>
                        <div className="ml-1 text-sm text-gray-500">
                          ({due.familyId.slNo})
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm font-medium text-gray-900">
                        {formatCurrency(due.amount)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-500">
                        {formatDate(due.dueDate)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {due.status === 'new' ? (
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          <CheckCircle size={14} className="mr-1" /> New
                        </span>
                      ) : (
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                          <AlertCircle size={14} className="mr-1" /> Already Exists
                        </span>
                      )}
                    </td>
                  </tr>
                ))}
                
                {generatedDues.length === 0 && (
                  <tr>
                    <td colSpan={4} className="px-6 py-4 text-center text-gray-500">
                      No dues to generate with the current filters
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          
          {generatedDues.length > 0 && (
            <div className="mt-6 flex justify-end">
              <button
                type="button"
                onClick={generateDues}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center"
                disabled={isGenerating}
              >
                {isGenerating ? (
                  <Loader2 size={18} className="animate-spin mr-2" />
                ) : (
                  <Save size={18} className="mr-2" />
                )}
                Confirm & Generate Dues
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
