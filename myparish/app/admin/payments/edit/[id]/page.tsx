'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { paymentsApi } from '@/lib/api';
import { useTheme } from '@/app/context/ThemeContext';
import { Loader2, Save, ArrowLeft, Calendar, DollarSign, CreditCard } from 'lucide-react';
import Link from 'next/link';
import { toast } from 'react-toastify';

interface Payment {
  _id: string;
  familyId: {
    _id: string;
    headName: string;
    slNo: string;
  };
  paymentTypeId: {
    _id: string;
    name: string;
  };
  amount: number;
  paymentDate: string;
  year: number;
  period?: string;
  receiptNumber: string;
  paymentMethod: string;
  collectedBy: {
    _id: string;
    name: string;
  };
  notes?: string;
}

export default function EditPaymentPage() {
  const params = useParams();
  const router = useRouter();
  const { colors } = useTheme();
  const [payment, setPayment] = useState<Payment | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  
  const [formData, setFormData] = useState({
    amount: 0,
    paymentDate: '',
    receiptNumber: '',
    paymentMethod: 'cash',
    notes: ''
  });

  useEffect(() => {
    if (params.id) {
      fetchPayment(params.id as string);
    }
  }, [params.id]);

  const fetchPayment = async (id: string) => {
    try {
      setIsLoading(true);
      const data = await paymentsApi.getById(id);
      setPayment(data);
      
      // Format date for input field
      const paymentDate = new Date(data.paymentDate);
      const formattedDate = paymentDate.toISOString().split('T')[0];
      
      setFormData({
        amount: data.amount,
        paymentDate: formattedDate,
        receiptNumber: data.receiptNumber,
        paymentMethod: data.paymentMethod,
        notes: data.notes || ''
      });
    } catch (error) {
      console.error('Failed to fetch payment:', error);
      toast.error('Failed to load payment details');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.amount <= 0) {
      toast.error('Amount must be greater than zero');
      return;
    }
    
    try {
      setIsSaving(true);
      
      await paymentsApi.update(params.id as string, formData);
      
      toast.success('Payment updated successfully');
      router.push(`/admin/payments/${params.id}`);
    } catch (error) {
      console.error('Failed to update payment:', error);
      toast.error('Failed to update payment');
    } finally {
      setIsSaving(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2">Loading payment details...</span>
      </div>
    );
  }

  if (!payment) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <p className="text-red-500 font-medium">Payment not found</p>
        <Link href="/admin/payments" className="mt-4 text-blue-600 hover:underline">
          Return to payments
        </Link>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="flex items-center mb-6">
        <Link href={`/admin/payments/${payment._id}`} className="mr-4">
          <ArrowLeft className="h-5 w-5" />
        </Link>
        <h1 className="text-2xl font-bold">Edit Payment</h1>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h2 className="text-lg font-medium mb-2">Payment Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-500">Family</p>
              <p className="font-medium">{payment.familyId.headName} ({payment.familyId.slNo})</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Payment Type</p>
              <p className="font-medium">{payment.paymentTypeId.name}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Year / Period</p>
              <p className="font-medium">{payment.year} {payment.period && `(${payment.period})`}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Collected By</p>
              <p className="font-medium">{payment.collectedBy.name}</p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="amount">
                Amount <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <DollarSign size={16} className="text-gray-500" />
                </div>
                <input
                  type="number"
                  id="amount"
                  name="amount"
                  value={formData.amount}
                  onChange={handleInputChange}
                  className="w-full pl-10 p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0.00"
                  required
                  min="0"
                  step="0.01"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="paymentDate">
                Payment Date <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Calendar size={16} className="text-gray-500" />
                </div>
                <input
                  type="date"
                  id="paymentDate"
                  name="paymentDate"
                  value={formData.paymentDate}
                  onChange={handleInputChange}
                  className="w-full pl-10 p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="receiptNumber">
                Receipt Number <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="receiptNumber"
                name="receiptNumber"
                value={formData.receiptNumber}
                onChange={handleInputChange}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="paymentMethod">
                Payment Method <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <CreditCard size={16} className="text-gray-500" />
                </div>
                <select
                  id="paymentMethod"
                  name="paymentMethod"
                  value={formData.paymentMethod}
                  onChange={handleInputChange}
                  className="w-full pl-10 p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="cash">Cash</option>
                  <option value="check">Check</option>
                  <option value="bank_transfer">Bank Transfer</option>
                  <option value="online">Online Payment</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium mb-1" htmlFor="notes">
                Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                rows={3}
                placeholder="Add any additional notes here..."
              />
            </div>
          </div>

          <div className="mt-6 flex justify-end">
            <Link
              href={`/admin/payments/${payment._id}`}
              className="px-4 py-2 border rounded text-gray-700 hover:bg-gray-50 mr-2"
            >
              Cancel
            </Link>
            <button
              type="submit"
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center"
              disabled={isSaving}
            >
              {isSaving ? (
                <Loader2 size={18} className="animate-spin mr-2" />
              ) : (
                <Save size={18} className="mr-2" />
              )}
              Save Changes
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
