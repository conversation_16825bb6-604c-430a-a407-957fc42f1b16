'use client';

import { useState, useEffect, useRef } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { paymentsApi } from '@/lib/api';
import { useTheme } from '@/app/context/ThemeContext';
import { Loader2, Printer, Download, ArrowLeft, Share2 } from 'lucide-react';
import Link from 'next/link';
import { toast } from 'react-toastify';
import { useReactToPrint } from 'react-to-print';

interface Payment {
  _id: string;
  familyId: {
    _id: string;
    headName: string;
    slNo: string;
    address: string;
    phone: string;
  };
  paymentTypeId: {
    _id: string;
    name: string;
  };
  amount: number;
  paymentDate: string;
  year: number;
  period?: string;
  receiptNumber: string;
  paymentMethod: string;
  collectedBy: {
    _id: string;
    name: string;
  };
  notes?: string;
  createdAt: string;
}

export default function PaymentReceiptPage() {
  const params = useParams();
  const router = useRouter();
  const { colors } = useTheme();
  const [payment, setPayment] = useState<Payment | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const receiptRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (params.id) {
      fetchPayment(params.id as string);
    }
  }, [params.id]);

  const fetchPayment = async (id: string) => {
    try {
      setIsLoading(true);
      const data = await paymentsApi.getById(id);
      setPayment(data);
    } catch (error) {
      console.error('Failed to fetch payment:', error);
      toast.error('Failed to load payment details');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePrint = useReactToPrint({
    content: () => receiptRef.current,
    documentTitle: `Receipt-${payment?.receiptNumber || 'Payment'}`,
    onAfterPrint: () => console.log('Printed successfully')
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getPaymentMethodLabel = (method: string) => {
    switch (method) {
      case 'cash': return 'Cash';
      case 'check': return 'Check';
      case 'bank_transfer': return 'Bank Transfer';
      case 'online': return 'Online';
      case 'other': return 'Other';
      default: return method;
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2">Loading receipt...</span>
      </div>
    );
  }

  if (!payment) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">Payment not found</p>
        <Link href="/admin/payments" className="text-blue-600 hover:underline mt-4 inline-block">
          Back to Payments
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Link href="/admin/payments" className="mr-4 text-gray-500 hover:text-gray-700">
            <ArrowLeft size={20} />
          </Link>
          <h1 className="text-xl font-semibold">Payment Receipt</h1>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={handlePrint}
            className="bg-blue-600 text-white px-3 py-2 rounded hover:bg-blue-700 flex items-center"
          >
            <Printer size={18} className="mr-2" />
            Print Receipt
          </button>
        </div>
      </div>

      {/* Receipt */}
      <div className="bg-white rounded-lg shadow-md p-8 max-w-3xl mx-auto" ref={receiptRef}>
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold">St. Mary's Parish</h2>
          <p className="text-gray-600">123 Main Street, Anytown, USA</p>
          <p className="text-gray-600">Phone: (*************</p>
          <div className="mt-4 border-t border-b border-gray-200 py-2">
            <h3 className="text-xl font-semibold">PAYMENT RECEIPT</h3>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 mb-6">
          <div>
            <p className="text-gray-600">Receipt No:</p>
            <p className="font-semibold">{payment.receiptNumber}</p>
          </div>
          <div className="text-right">
            <p className="text-gray-600">Date:</p>
            <p className="font-semibold">{formatDate(payment.paymentDate)}</p>
          </div>
        </div>

        <div className="mb-6">
          <p className="text-gray-600">Received from:</p>
          <p className="font-semibold">{payment.familyId.headName} (ID: {payment.familyId.slNo})</p>
          <p className="text-gray-600 mt-1">{payment.familyId.address}</p>
          {payment.familyId.phone && (
            <p className="text-gray-600">Phone: {payment.familyId.phone}</p>
          )}
        </div>

        <div className="border border-gray-200 rounded-lg overflow-hidden mb-6">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Period
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {payment.paymentTypeId.name}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {payment.period ? `${payment.period}, ${payment.year}` : payment.year}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right">
                  {formatCurrency(payment.amount)}
                </td>
              </tr>
              <tr className="bg-gray-50">
                <td colSpan={2} className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right">
                  Total:
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 text-right">
                  {formatCurrency(payment.amount)}
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div className="grid grid-cols-2 gap-4 mb-6">
          <div>
            <p className="text-gray-600">Payment Method:</p>
            <p className="font-semibold">{getPaymentMethodLabel(payment.paymentMethod)}</p>
          </div>
          <div className="text-right">
            <p className="text-gray-600">Collected By:</p>
            <p className="font-semibold">{payment.collectedBy.name}</p>
          </div>
        </div>

        {payment.notes && (
          <div className="mb-6">
            <p className="text-gray-600">Notes:</p>
            <p className="text-gray-800">{payment.notes}</p>
          </div>
        )}

        <div className="mt-10 pt-6 border-t border-gray-200 flex justify-between">
          <div>
            <p className="text-gray-600 text-sm">Thank you for your contribution!</p>
          </div>
          <div className="text-right">
            <p className="text-gray-600 text-sm">Authorized Signature</p>
            <div className="h-10 mt-2"></div>
            <p className="text-gray-600 text-sm">_________________________</p>
          </div>
        </div>

        <div className="mt-6 text-center text-xs text-gray-500">
          <p>This is a computer-generated receipt and does not require a physical signature.</p>
          <p>Generated on: {formatDate(new Date().toISOString())}</p>
        </div>
      </div>
    </div>
  );
}