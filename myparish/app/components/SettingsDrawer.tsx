'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useTheme } from '../context/ThemeContext';
import { 
  Settings, 
  ChevronDown, 
  ChevronRight, 
  Globe, 
  Mail, 
  Database, 
  Shield, 
  Users, 
  Image as ImageIcon,
  Palette,
  Building,
  Phone,
  MapPin,
  Layout,
  Type,
  FileImage,
  Album,
  UserPlus,
  Lock,
  Key,
  Bell,
  DollarSign
} from 'lucide-react';

interface SettingsDrawerProps {
  activeGroup: string;
}

export default function SettingsDrawer({ activeGroup }: SettingsDrawerProps) {
  const { colors } = useTheme();
  const pathname = usePathname();
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({});
  
  // Update expanded groups when activeGroup changes
  useEffect(() => {
    setExpandedGroups({
      ...expandedGroups,
      [activeGroup]: true
    });
    
    // Listen for custom event from parent component
    const handleGroupChange = (e: CustomEvent) => {
      setExpandedGroups({
        ...expandedGroups,
        [e.detail.group]: true
      });
    };
    
    window.addEventListener('settingsGroupChange', handleGroupChange as EventListener);
    
    return () => {
      window.removeEventListener('settingsGroupChange', handleGroupChange as EventListener);
    };
  }, [activeGroup]);

  const settingsGroups = {
    general: {
      title: 'General Settings',
      icon: <Settings size={18} />,
      items: [
        { name: 'Parish Information', icon: <Building size={16} />, path: '/admin/settings' },
        { name: 'Contact Details', icon: <Phone size={16} /> },
        { name: 'Location', icon: <MapPin size={16} /> },
      ]
    },
    appearance: {
      title: 'Appearance',
      icon: <Palette size={18} />,
      items: [
        { name: 'Theme', icon: <Palette size={16} /> },
        { name: 'Layout', icon: <Layout size={16} /> },
        { name: 'Typography', icon: <Type size={16} /> },
        { name: 'Logo & Icons', icon: <FileImage size={16} /> },
      ]
    },
    content: {
      title: 'Content',
      icon: <Globe size={18} />,
      items: [
        { name: 'Offering Types', icon: <Album size={16} />, path: '/admin/settings/offering-types' },
        { name: 'Payment Types', icon: <DollarSign size={16} />, path: '/admin/settings/payment-types' },
      ]
    },
    media: {
      title: 'Media',
      icon: <ImageIcon size={18} />,
      items: [
        { name: 'Parish Images', icon: <FileImage size={16} /> },
        { name: 'Gallery Management', icon: <Album size={16} /> },
        { name: 'Media Library', icon: <ImageIcon size={16} /> },
      ]
    },
    users: {
      title: 'User Management',
      icon: <Users size={18} />,
      items: [
        { name: 'User Accounts', icon: <Users size={16} /> },
        { name: 'Roles & Permissions', icon: <UserPlus size={16} /> },
        { name: 'User Activity', icon: <Bell size={16} /> },
      ]
    },
    system: {
      title: 'System Settings',
      icon: <Database size={18} />,
      items: [
        { name: 'Backup & Restore', icon: <Database size={16} /> },
        { name: 'Email Configuration', icon: <Mail size={16} /> },
        { name: 'System Logs', icon: <Database size={16} /> },
      ]
    },
    security: {
      title: 'Security',
      icon: <Shield size={18} />,
      items: [
        { name: 'Authentication', icon: <Lock size={16} /> },
        { name: 'Password Policy', icon: <Key size={16} /> },
        { name: 'Data Protection', icon: <Shield size={16} /> },
      ]
    },
  };

  const toggleGroup = (group: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [group]: !prev[group]
    }));
  };

  return (
    <div className="h-full overflow-y-auto py-4">
      <div className="px-4 mb-6">
        <h2 className="text-xl font-semibold flex items-center">
          <Settings className="mr-2" />
          Settings
        </h2>
        <p className="text-sm opacity-70 mt-1">Manage your parish system</p>
      </div>

      <div className="space-y-2 px-2">
        {Object.entries(settingsGroups).map(([key, group]) => {
          const isExpanded = expandedGroups[key];
          const isActive = activeGroup === key;
          
          return (
            <div key={key} className="rounded-lg overflow-hidden">
              <button
                onClick={() => toggleGroup(key)}
                className="w-full flex items-center justify-between p-3 rounded-lg transition-colors"
                style={{ 
                  backgroundColor: isActive ? `${colors.primary}15` : 'transparent',
                  color: isActive ? colors.primary : colors.foreground
                }}
              >
                <div className="flex items-center">
                  <span className="mr-3">{group.icon}</span>
                  <span className="font-medium">{group.title}</span>
                </div>
                {isExpanded ? <ChevronDown size={18} /> : <ChevronRight size={18} />}
              </button>
              
              {isExpanded && (
                <div className="pl-10 pr-3 pb-2 space-y-1">
                  {group.items.map((item, itemIndex) => (
                    <button
                      key={itemIndex}
                      className="w-full flex items-center py-2 px-3 rounded-md text-sm transition-colors hover:bg-gray-100"
                      style={{
                        color: colors.foreground
                      }}
                    >
                      <span className="mr-2 opacity-70">{item.icon}</span>
                      {item.name}
                    </button>
                  ))}
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}
