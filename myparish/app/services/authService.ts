import apiClient from './apiClient';
import { User } from '../types';

interface LoginResponse {
  token: string;
  user: User;
}

interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterData {
  name: string;
  email: string;
  password: string;
  role?: string;
}

export const login = async (credentials: LoginCredentials): Promise<LoginResponse> => {
  const response = await apiClient.post('/auth/login', credentials);
  // Store token and user in localStorage
  localStorage.setItem('token', response.data.token);
  localStorage.setItem('user', JSON.stringify(response.data.user));
  return response.data;
};

export const register = async (userData: RegisterData): Promise<LoginResponse> => {
  const response = await apiClient.post('/auth/register', userData);
  return response.data;
};

export const getCurrentUser = async (): Promise<User> => {
  const response = await apiClient.get('/auth/me');
  return response.data;
};

export const logout = (): void => {
  // Clear both localStorage and cookies
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  document.cookie = 'isAuthenticated=; path=/; max-age=0';
  document.cookie = 'user=; path=/; max-age=0';
};
