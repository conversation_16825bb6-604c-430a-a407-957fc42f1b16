import mongoose, { Schema, Document } from 'mongoose';

export interface IPaymentDue extends Document {
  familyId: mongoose.Types.ObjectId;
  paymentTypeId: mongoose.Types.ObjectId;
  amount: number;
  dueDate: Date;
  year: number;
  period?: string;
  isPaid: boolean;
  partialPayment: number;
  createdAt: Date;
  updatedAt: Date;
}

const PaymentDueSchema: Schema = new Schema(
  {
    familyId: {
      type: Schema.Types.ObjectId,
      ref: 'Family',
      required: true
    },
    paymentTypeId: {
      type: Schema.Types.ObjectId,
      ref: 'PaymentType',
      required: true
    },
    amount: {
      type: Number,
      required: true,
      min: 0
    },
    dueDate: {
      type: Date,
      required: true
    },
    year: {
      type: Number,
      required: true
    },
    period: {
      type: String,
      enum: ['Q1', 'Q2', 'Q3', 'Q4', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', ''],
      default: ''
    },
    isPaid: {
      type: Boolean,
      default: false
    },
    partialPayment: {
      type: Number,
      default: 0,
      min: 0
    }
  },
  {
    timestamps: true
  }
);

// Create compound index to prevent duplicate dues
PaymentDueSchema.index(
  { 
    familyId: 1, 
    paymentTypeId: 1, 
    year: 1, 
    period: 1 
  }, 
  { 
    unique: true,
    partialFilterExpression: { period: { $exists: true } }
  }
);

PaymentDueSchema.index(
  { 
    familyId: 1, 
    paymentTypeId: 1, 
    year: 1
  }, 
  { 
    unique: true,
    partialFilterExpression: { period: { $exists: false } }
  }
);

export default mongoose.model<IPaymentDue>('PaymentDue', PaymentDueSchema);
