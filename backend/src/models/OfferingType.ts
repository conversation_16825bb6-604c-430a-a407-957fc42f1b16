import mongoose, { Document, Schema } from 'mongoose';

export interface IOfferingType extends Document {
  name: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const OfferingTypeSchema = new Schema<IOfferingType>({
  name: { type: String, required: true, unique: true },
  isActive: { type: Boolean, default: true },
}, { timestamps: true });

export default mongoose.model<IOfferingType>('OfferingType', OfferingTypeSchema);