import mongoose, { Document, Schema } from 'mongoose';

export interface IPaymentType extends Document {
  name: string;
  description: string;
  amount: number;
  frequency: 'yearly' | 'monthly' | 'quarterly' | 'one-time';
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const PaymentTypeSchema = new Schema<IPaymentType>({
  name: { type: String, required: true, unique: true },
  description: { type: String },
  amount: { type: Number, required: true },
  frequency: { 
    type: String, 
    enum: ['yearly', 'monthly', 'quarterly', 'one-time'],
    default: 'yearly'
  },
  isActive: { type: Boolean, default: true },
}, { timestamps: true });

export default mongoose.model<IPaymentType>('PaymentType', PaymentTypeSchema);