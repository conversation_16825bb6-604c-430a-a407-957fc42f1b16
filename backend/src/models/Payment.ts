import mongoose, { Schema, Document } from 'mongoose';

export interface IPayment extends Document {
  familyId: mongoose.Types.ObjectId;
  paymentTypeId: mongoose.Types.ObjectId;
  dueId?: mongoose.Types.ObjectId;
  amount: number;
  paymentDate: Date;
  year: number;
  period?: string;
  receiptNumber: string;
  paymentMethod: string;
  notes?: string;
  collectedBy: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const PaymentSchema: Schema = new Schema(
  {
    familyId: {
      type: Schema.Types.ObjectId,
      ref: 'Family',
      required: true
    },
    paymentTypeId: {
      type: Schema.Types.ObjectId,
      ref: 'PaymentType',
      required: true
    },
    dueId: {
      type: Schema.Types.ObjectId,
      ref: 'PaymentDue',
      required: false
    },
    amount: {
      type: Number,
      required: true,
      min: 0
    },
    paymentDate: {
      type: Date,
      required: true
    },
    year: {
      type: Number,
      required: true
    },
    period: {
      type: String,
      enum: ['Q1', 'Q2', 'Q3', 'Q4', 'H1', 'H2', 'Monthly'],
      required: false
    },
    receiptNumber: {
      type: String,
      required: true
    },
    paymentMethod: {
      type: String,
      enum: ['cash', 'check', 'bank_transfer', 'upi', 'card', 'other'],
      default: 'cash'
    },
    notes: {
      type: String
    },
    collectedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    }
  },
  {
    timestamps: true
  }
);

// Create indexes for common queries
PaymentSchema.index({ familyId: 1, paymentDate: -1 });
PaymentSchema.index({ paymentTypeId: 1, year: 1 });
PaymentSchema.index({ dueId: 1 });

export default mongoose.model<IPayment>('Payment', PaymentSchema);
