import express, { Router } from 'express';
import PaymentType from '../models/PaymentType.ts';
import { authenticate, authorize } from '../middleware/auth.ts';

const router: Router = express.Router();

// Get all payment types
router.get('/', authenticate, async (req, res) => {
  try {
    const paymentTypes = await PaymentType.find().sort({ name: 1 });
    res.json(paymentTypes);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Get payment type by ID
router.get('/:id', authenticate, async (req, res) => {
  try {
    const paymentType = await PaymentType.findById(req.params.id);
    if (!paymentType) {
      return res.status(404).json({ message: 'Payment type not found' });
    }
    res.json(paymentType);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Create new payment type
router.post(
  '/',
  authenticate,
  authorize(['Admin']),
  async (req, res) => {
    try {
      const { name, description, amount, frequency } = req.body;
      
      // Check if payment type already exists
      const existingType = await PaymentType.findOne({ name });
      if (existingType) {
        return res.status(400).json({ message: 'Payment type already exists' });
      }
      
      const paymentType = new PaymentType({ 
        name, 
        description, 
        amount, 
        frequency 
      });
      await paymentType.save();
      
      res.status(201).json(paymentType);
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

// Update payment type
router.put(
  '/:id',
  authenticate,
  authorize(['Admin']),
  async (req, res) => {
    try {
      const { name, description, amount, frequency, isActive } = req.body;
      
      const paymentType = await PaymentType.findByIdAndUpdate(
        req.params.id,
        { name, description, amount, frequency, isActive },
        { new: true }
      );
      
      if (!paymentType) {
        return res.status(404).json({ message: 'Payment type not found' });
      }
      
      res.json(paymentType);
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

// Delete payment type
router.delete(
  '/:id',
  authenticate,
  authorize(['Admin']),
  async (req, res) => {
    try {
      const paymentType = await PaymentType.findByIdAndDelete(req.params.id);
      
      if (!paymentType) {
        return res.status(404).json({ message: 'Payment type not found' });
      }
      
      res.json({ message: 'Payment type deleted successfully' });
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

export default router;