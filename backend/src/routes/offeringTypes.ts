import express, { Router } from 'express';
import OfferingType from '../models/OfferingType.ts';
import { authenticate, authorize } from '../middleware/auth.ts';

const router: Router = express.Router();

// Get all offering types
router.get('/', authenticate, async (req, res) => {
  try {
    const offeringTypes = await OfferingType.find().sort({ name: 1 });
    res.json(offeringTypes);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Create new offering type
router.post(
  '/',
  authenticate,
  authorize(['Admin']),
  async (req, res) => {
    try {
      const { name } = req.body;
      
      // Check if offering type already exists
      const existingType = await OfferingType.findOne({ name });
      if (existingType) {
        return res.status(400).json({ message: 'Offering type already exists' });
      }
      
      const offeringType = new OfferingType({ name });
      await offeringType.save();
      
      res.status(201).json(offeringType);
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

// Update offering type
router.put(
  '/:id',
  authenticate,
  authorize(['Admin']),
  async (req, res) => {
    try {
      const { name, isActive } = req.body;
      
      const offeringType = await OfferingType.findByIdAndUpdate(
        req.params.id,
        { name, isActive },
        { new: true }
      );
      
      if (!offeringType) {
        return res.status(404).json({ message: 'Offering type not found' });
      }
      
      res.json(offeringType);
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

// Delete offering type
router.delete(
  '/:id',
  authenticate,
  authorize(['Admin']),
  async (req, res) => {
    try {
      const offeringType = await OfferingType.findByIdAndDelete(req.params.id);
      
      if (!offeringType) {
        return res.status(404).json({ message: 'Offering type not found' });
      }
      
      res.json({ message: 'Offering type deleted successfully' });
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

export default router;